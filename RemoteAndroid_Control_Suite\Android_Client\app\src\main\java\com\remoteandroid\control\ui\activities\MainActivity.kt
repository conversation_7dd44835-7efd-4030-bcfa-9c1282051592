package com.remoteandroid.control.ui.activities

import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.activity.result.contract.ActivityResultContracts
import com.remoteandroid.control.R
import com.remoteandroid.control.managers.PermissionManager
import com.remoteandroid.control.services.core.RemoteControlForegroundService
import com.remoteandroid.control.utils.PermissionHelper

/**
 * RemoteAndroid Control Suite - 主Activity
 * 负责应用的主界面显示和基础功能入口
 */
class MainActivity : AppCompatActivity() {

    private lateinit var permissionManager: PermissionManager
    private lateinit var statusText: TextView
    private lateinit var permissionButton: Button

    // 状态标记
    private var isScreenCapturePermissionRequested = false
    private var isServiceStarted = false

    // 屏幕录制权限申请
    private val screenCapturePermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        Log.d("MainActivity", "=== 权限申请回调触发 ===")
        Log.d("MainActivity", "resultCode: ${result.resultCode}")
        Log.d("MainActivity", "RESULT_OK: $RESULT_OK")
        Log.d("MainActivity", "resultData: ${result.data}")

        isScreenCapturePermissionRequested = false // 重置申请状态

        if (result.resultCode == RESULT_OK) {
            Log.d("MainActivity", "✅ 屏幕录制权限获得成功")

            // 屏幕录制权限获得，设置权限状态
            PermissionHelper.setScreenCapturePermission(true)
            Log.d("MainActivity", "权限状态已设置为true")

            // 启动服务，并传递屏幕录制权限数据
            startRemoteControlService(result.resultCode, result.data)
            Log.d("MainActivity", "startRemoteControlService已调用")

            // 通知服务屏幕录制权限已获得
            notifyServiceScreenCapturePermissionGranted(result.resultCode, result.data)
            Log.d("MainActivity", "服务通知已发送")

            // 更新UI状态
            statusText.text = "✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 正在启动屏幕录制..."

        } else {
            Log.w("MainActivity", "❌ 屏幕录制权限被拒绝，resultCode: ${result.resultCode}")
            statusText.text = "✅ 基础权限已授予\n❌ 屏幕录制权限被拒绝\n请重新申请权限以启用完整功能"

            // 通知服务屏幕录制权限被拒绝
            notifyServiceScreenCapturePermissionDenied()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        permissionManager = PermissionManager(this)

        // 【关键检查点1】初始化MediaProjectionManager
        try {
            val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            Log.d("MainActivity", "MediaProjectionManager initialized: ${mediaProjectionManager != null}")
        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to initialize MediaProjectionManager", e)
        }

        // 初始化UI组件
        initViews()

        // 检查权限状态
        checkPermissions()

        // 检查是否是从服务启动的屏幕录制权限请求
        handleScreenCaptureRequest()
    }

    override fun onResume() {
        super.onResume()
        // 每次回到前台都检查权限状态
        updatePermissionStatus()
    }

    /**
     * 初始化界面组件
     */
    private fun initViews() {
        statusText = findViewById(R.id.tv_status)
        permissionButton = findViewById(R.id.btn_permission_guide)

        permissionButton.setOnClickListener {
            if (permissionButton.text == "手动申请屏幕录制权限") {
                // 手动申请屏幕录制权限
                requestScreenCapturePermission()
            } else {
                // 重新检查权限或启动权限引导
                if (permissionManager.areAllPermissionsGranted()) {
                    updatePermissionStatus()
                } else {
                    startPermissionGuide()
                }
            }
        }
    }

    /**
     * 检查必要权限
     */
    private fun checkPermissions() {
        if (!permissionManager.areAllPermissionsGranted()) {
            // 如果权限不完整，直接跳转到权限引导页面
            startPermissionGuide()
        } else {
            updatePermissionStatus()
        }
    }

    /**
     * 更新权限状态显示
     */
    private fun updatePermissionStatus() {
        val permissionStatus = permissionManager.getAllPermissionStatus()
        val missingPermissions = permissionManager.getMissingPermissions()

        if (missingPermissions.isEmpty()) {
            // 基础权限已授予，立即启动服务
            if (!isServiceStarted) {
                startRemoteControlService() // 先启动服务，不需要屏幕录制权限
                statusText.text = "✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"
                permissionButton.text = "手动申请屏幕录制权限"
            } else {
                statusText.text = "✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"
                permissionButton.text = "手动申请屏幕录制权限"
            }
        } else {
            val statusBuilder = StringBuilder("⚠️ 权限状态检查\n\n")

            permissionStatus.forEach { (permission, status) ->
                val emoji = if (status == "granted") "✅" else "❌"
                val permissionName = when (permission) {
                    "accessibility" -> "无障碍权限"
                    "overlay" -> "悬浮窗权限"
                    "storage" -> "存储权限"
                    "battery_optimization" -> "电池优化白名单"
                    else -> permission
                }
                statusBuilder.append("$emoji $permissionName\n")
            }

            statusBuilder.append("\n缺少 ${missingPermissions.size} 个权限，请点击下方按钮完成权限申请")
            statusText.text = statusBuilder.toString()
            permissionButton.text = "申请权限"
        }
    }

    /**
     * 启动权限引导页面
     */
    private fun startPermissionGuide() {
        // 重置状态，允许重新申请权限
        isScreenCapturePermissionRequested = false
        isServiceStarted = false

        val intent = Intent(this, PermissionGuideActivity::class.java)
        startActivity(intent)
    }

    /**
     * 申请屏幕录制权限
     */
    // 【关键检查点2】权限申请方法
    private fun requestScreenCapturePermission() {
        Log.d("MainActivity", "=== requestScreenCapturePermission called ===")
        Log.d("MainActivity", "isScreenCapturePermissionRequested: $isScreenCapturePermissionRequested")

        if (isScreenCapturePermissionRequested) {
            Log.w("MainActivity", "权限申请已在进行中，跳过重复申请")
            return // 防止重复申请
        }

        try {
            Log.d("MainActivity", "开始申请屏幕录制权限")
            isScreenCapturePermissionRequested = true

            val mediaProjectionManager = getSystemService(MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            Log.d("MainActivity", "MediaProjectionManager obtained: ${mediaProjectionManager != null}")

            val captureIntent = mediaProjectionManager.createScreenCaptureIntent()
            Log.d("MainActivity", "ScreenCaptureIntent created: ${captureIntent != null}")
            Log.d("MainActivity", "Intent details: $captureIntent")

            screenCapturePermissionLauncher.launch(captureIntent)
            Log.d("MainActivity", "权限申请Intent已启动")

            // 更新UI状态
            statusText.text = "✅ 基础权限已授予\n🔄 正在申请屏幕录制权限...\n请在弹出的对话框中点击'立即开始'"

        } catch (e: Exception) {
            Log.e("MainActivity", "启动权限申请失败", e)
            isScreenCapturePermissionRequested = false
            statusText.text = "✅ 基础权限已授予\n❌ 屏幕录制权限申请失败: ${e.message}"
        }
    }

    /**
     * 启动远程控制前台服务
     */
    private fun startRemoteControlService(resultCode: Int = -1, resultData: Intent? = null) {
        Log.d("MainActivity", "startRemoteControlService called, isServiceStarted: $isServiceStarted")
        // 强制启动服务，不检查isServiceStarted状态
        // if (isServiceStarted) {
        //     return // 防止重复启动
        // }

        try {
            val serviceIntent = Intent(this, RemoteControlForegroundService::class.java)
            serviceIntent.action = "START_SERVICE"

            // 如果有屏幕录制权限数据，传递给服务
            if (resultCode != -1 && resultData != null) {
                serviceIntent.putExtra("SCREEN_CAPTURE_RESULT_CODE", resultCode)
                serviceIntent.putExtra("SCREEN_CAPTURE_RESULT_DATA", resultData)
            }

            startForegroundService(serviceIntent)

            isServiceStarted = true
            // 更新状态显示
            statusText.text = "✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接..."
        } catch (e: Exception) {
            statusText.text = "✅ 所有权限已授予\n❌ 服务启动失败: ${e.message}"
        }
    }

    /**
     * 处理来自服务的屏幕录制权限请求
     */
    private fun handleScreenCaptureRequest() {
        val requestScreenCapture = intent.getBooleanExtra("REQUEST_SCREEN_CAPTURE", false)
        if (requestScreenCapture) {
            // 清除Intent标记，避免重复处理
            intent.removeExtra("REQUEST_SCREEN_CAPTURE")

            // 直接申请屏幕录制权限
            if (!isScreenCapturePermissionRequested) {
                statusText.text = "🎯 PC端请求屏幕录制权限\n正在申请屏幕录制权限..."
                requestScreenCapturePermission()
            }
        }
    }

    /**
     * 通知服务屏幕录制权限已获得
     */
    private fun notifyServiceScreenCapturePermissionGranted(resultCode: Int, resultData: Intent?) {
        try {
            val serviceIntent = Intent(this, RemoteControlForegroundService::class.java)
            serviceIntent.action = "SCREEN_CAPTURE_PERMISSION_GRANTED"
            serviceIntent.putExtra("SCREEN_CAPTURE_RESULT_CODE", resultCode)
            serviceIntent.putExtra("SCREEN_CAPTURE_RESULT_DATA", resultData)
            startService(serviceIntent)
        } catch (e: Exception) {
            Log.e("MainActivity", "通知服务权限获得失败", e)
        }
    }

    /**
     * 通知服务屏幕录制权限被拒绝
     */
    private fun notifyServiceScreenCapturePermissionDenied() {
        try {
            val serviceIntent = Intent(this, RemoteControlForegroundService::class.java)
            serviceIntent.action = "SCREEN_CAPTURE_PERMISSION_DENIED"
            startService(serviceIntent)
        } catch (e: Exception) {
            Log.e("MainActivity", "通知服务权限被拒绝失败", e)
        }
    }
}
