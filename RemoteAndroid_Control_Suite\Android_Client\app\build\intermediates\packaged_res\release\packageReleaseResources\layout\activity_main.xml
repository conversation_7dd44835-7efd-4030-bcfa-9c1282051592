<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@color/background_primary"
    tools:context=".ui.activities.MainActivity">

    <!-- 顶部标题区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_marginBottom="32dp">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@mipmap/ic_launcher"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="RemoteAndroid Control"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Android远程控制客户端"
            android:textSize="16sp"
            android:textColor="@color/text_secondary" />

    </LinearLayout>

    <!-- 状态信息卡片 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/card_background">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="20dp">

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="正在初始化..."
                android:textSize="16sp"
                android:textColor="@color/text_primary"
                android:lineSpacingExtra="4dp"
                android:gravity="center" />

        </ScrollView>

    </androidx.cardview.widget.CardView>

    <!-- 底部按钮 -->
    <Button
        android:id="@+id/btn_permission_guide"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:text="申请权限"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/white"
        android:background="@drawable/button_primary" />

</LinearLayout>
