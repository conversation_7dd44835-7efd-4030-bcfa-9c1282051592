package com.remoteandroid.control.services.core

import android.app.Activity
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Base64
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import com.remoteandroid.control.R
import com.remoteandroid.control.constants.AppConstants
import com.remoteandroid.control.models.network.ScreenDataMessage
import com.remoteandroid.control.services.network.NetworkClient
import com.remoteandroid.control.utils.ImageProcessor
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer

/**
 * RemoteAndroid Control Suite - 屏幕录制服务
 * 负责捕获屏幕内容并通过网络传输到PC端
 */
class ScreenCaptureService : Service() {
    
    companion object {
        private const val TAG = "ScreenCaptureService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "screen_capture_channel"
        
        // 屏幕录制参数 (硬编码配置)
        private const val SCREEN_WIDTH = 720
        private const val SCREEN_HEIGHT = 1280
        private const val SCREEN_DPI = 320
        private const val FRAME_RATE = 15
        private const val JPEG_QUALITY = 30
        
        // 服务控制Action
        const val ACTION_START_CAPTURE = "start_capture"
        const val ACTION_STOP_CAPTURE = "stop_capture"
        const val EXTRA_RESULT_CODE = "result_code"
        const val EXTRA_RESULT_DATA = "result_data"

        // 静态状态管理
        @Volatile
        private var isServiceCapturing = false

        fun isCapturingStatic(): Boolean = isServiceCapturing
    }
    
    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var imageReader: ImageReader? = null
    private var networkClient: NetworkClient? = null
    private var imageProcessor: ImageProcessor? = null
    
    private val captureHandler = Handler(Looper.getMainLooper())
    private var isCapturing = false
    private var frameCount = 0
    private var lastCaptureTime = 0L
    
    // 屏幕参数
    private var screenWidth = SCREEN_WIDTH
    private var screenHeight = SCREEN_HEIGHT
    private var screenDensity = SCREEN_DPI
    
    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "ScreenCaptureService created")
        
        networkClient = NetworkClient.getInstance()
        imageProcessor = ImageProcessor()
        
        createNotificationChannel()
        initScreenParameters()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_CAPTURE -> {
                val resultCode = intent.getIntExtra(EXTRA_RESULT_CODE, -1)
                val resultData = intent.getParcelableExtra<Intent>(EXTRA_RESULT_DATA)

                Log.d(TAG, "=== 屏幕录制启动请求 ===")
                Log.d(TAG, "resultCode: $resultCode")
                Log.d(TAG, "resultData: $resultData")
                Log.d(TAG, "resultData extras: ${resultData?.extras}")

                if (resultCode == Activity.RESULT_OK && resultData != null) {
                    // 只有在有真实权限数据时才启动
                    Log.d(TAG, "✅ 检测到真实权限数据，启动屏幕录制")
                    startScreenCapture(resultCode, resultData)
                } else {
                    // 没有真实权限数据，请求权限申请
                    Log.e(TAG, "❌ 缺少真实权限数据，无法启动屏幕录制")
                    Log.e(TAG, "resultCode: $resultCode, resultData: $resultData")

                    // 请求权限申请
                    requestScreenCapturePermissionFromService()
                }
            }
            ACTION_STOP_CAPTURE -> {
                stopScreenCapture()
            }
        }

        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "ScreenCaptureService destroyed")
        stopScreenCapture()
    }
    
    /**
     * 初始化屏幕参数
     */
    private fun initScreenParameters() {
        val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val display = windowManager.defaultDisplay
            display.getRealMetrics(displayMetrics)
        } else {
            @Suppress("DEPRECATION")
            windowManager.defaultDisplay.getRealMetrics(displayMetrics)
        }
        
        // 计算适配后的屏幕尺寸 (保持宽高比，限制在720p)
        val aspectRatio = displayMetrics.widthPixels.toFloat() / displayMetrics.heightPixels.toFloat()
        
        if (aspectRatio > 1.0f) {
            // 横屏模式
            screenWidth = SCREEN_WIDTH
            screenHeight = (SCREEN_WIDTH / aspectRatio).toInt()
        } else {
            // 竖屏模式
            screenHeight = SCREEN_HEIGHT
            screenWidth = (SCREEN_HEIGHT * aspectRatio).toInt()
        }
        
        screenDensity = displayMetrics.densityDpi
        
        Log.d(TAG, "Screen parameters: ${screenWidth}x${screenHeight}, density: $screenDensity")
    }
    
    /**
     * 开始屏幕录制 (公共方法)
     */
    fun startCapture() {
        Log.w(TAG, "startCapture() called without permission data - using default parameters")
        // 使用默认参数启动屏幕录制
        startCapture(Activity.RESULT_OK, null)
    }

    /**
     * 开始屏幕录制 (带权限数据)
     */
    fun startCapture(resultCode: Int, resultData: Intent?) {
        try {
            Log.d(TAG, "开始屏幕录制，resultCode: $resultCode")

            if (resultCode != Activity.RESULT_OK) {
                Log.e(TAG, "屏幕录制权限被拒绝，resultCode: $resultCode")
                return
            }

            // 启动屏幕录制
            if (resultData != null) {
                startScreenCapture(resultCode, resultData)
            } else {
                Log.w(TAG, "resultData为null，使用默认Intent启动屏幕录制")
                startScreenCapture(resultCode, Intent())
            }

        } catch (e: Exception) {
            Log.e(TAG, "启动屏幕录制失败", e)
        }
    }

    /**
     * 停止屏幕录制 (公共方法)
     */
    fun stopCapture() {
        stopScreenCapture()
    }

    /**
     * 检查是否正在录制
     */
    fun isCapturing(): Boolean {
        return isCapturing
    }

    /**
     * 开始屏幕录制 (内部方法)
     */
    private fun startScreenCapture(resultCode: Int, resultData: Intent) {
        Log.d(TAG, "=== startScreenCapture called ===")
        Log.d(TAG, "resultCode: $resultCode")
        Log.d(TAG, "resultData: $resultData")
        Log.d(TAG, "resultData extras: ${resultData.extras}")

        if (isCapturing) {
            Log.w(TAG, "Screen capture already started")
            return
        }

        try {
            val mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            Log.d(TAG, "MediaProjectionManager obtained: $mediaProjectionManager")

            // 尝试创建MediaProjection
            Log.d(TAG, "Attempting to create MediaProjection...")
            mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, resultData)
            Log.d(TAG, "MediaProjection created: $mediaProjection")

            if (mediaProjection == null) {
                Log.e(TAG, "Failed to create MediaProjection - invalid permission data")
                Log.e(TAG, "resultCode: $resultCode, resultData: $resultData")
                Log.e(TAG, "resultData extras: ${resultData.extras}")
                stopSelf()
                return
            }
            
            setupImageReader()
            setupVirtualDisplay()
            
            isCapturing = true
            isServiceCapturing = true
            frameCount = 0
            lastCaptureTime = System.currentTimeMillis()

            startForeground(NOTIFICATION_ID, createNotification())
            
            Log.i(TAG, "Screen capture started successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start screen capture", e)
            stopScreenCapture()
        }
    }
    
    /**
     * 停止屏幕录制
     */
    private fun stopScreenCapture() {
        if (!isCapturing) {
            return
        }
        
        isCapturing = false
        isServiceCapturing = false

        virtualDisplay?.release()
        virtualDisplay = null
        
        imageReader?.close()
        imageReader = null
        
        mediaProjection?.stop()
        mediaProjection = null
        
        stopForeground(true)
        stopSelf()
        
        Log.i(TAG, "Screen capture stopped")
    }
    
    /**
     * 设置ImageReader
     */
    private fun setupImageReader() {
        imageReader = ImageReader.newInstance(screenWidth, screenHeight, PixelFormat.RGBA_8888, 2)
        
        imageReader?.setOnImageAvailableListener({ reader ->
            try {
                val image = reader.acquireLatestImage()
                if (image != null) {
                    Log.d(TAG, "Image captured: ${image.width}x${image.height}, format: ${image.format}")
                    processScreenImage(image)
                    image.close()
                } else {
                    Log.w(TAG, "Failed to acquire image from ImageReader")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing screen image", e)
            }
        }, captureHandler)
    }
    
    /**
     * 设置虚拟显示
     */
    private fun setupVirtualDisplay() {
        virtualDisplay = mediaProjection?.createVirtualDisplay(
            "RemoteAndroid-ScreenCapture",
            screenWidth,
            screenHeight,
            screenDensity,
            DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
            imageReader?.surface,
            null,
            null
        )
    }
    
    /**
     * 处理屏幕图像
     */
    private fun processScreenImage(image: Image) {
        val currentTime = System.currentTimeMillis()
        val frameInterval = 1000L / FRAME_RATE
        
        // 帧率控制
        if (currentTime - lastCaptureTime < frameInterval) {
            return
        }
        
        lastCaptureTime = currentTime
        frameCount++
        
        try {
            // 转换Image到Bitmap
            val bitmap = imageToBitmap(image)
            if (bitmap != null) {
                Log.d(TAG, "Bitmap created: ${bitmap.width}x${bitmap.height}")

                // 压缩为JPEG
                val jpegData = compressBitmapToJpeg(bitmap, JPEG_QUALITY)
                Log.d(TAG, "JPEG compressed: ${jpegData.size} bytes, quality: $JPEG_QUALITY%")

                // Base64编码
                val base64Data = Base64.encodeToString(jpegData, Base64.NO_WRAP)
                Log.d(TAG, "Base64 encoded: ${base64Data.length} characters")

                // 发送到PC端
                sendScreenData(base64Data, bitmap.width, bitmap.height)
                Log.d(TAG, "Screen data sent to PC, frame: $frameCount")

                bitmap.recycle()
            } else {
                Log.e(TAG, "Failed to convert Image to Bitmap")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error processing screen image", e)
        }
    }
    
    /**
     * Image转Bitmap
     */
    private fun imageToBitmap(image: Image): Bitmap? {
        return try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * screenWidth
            
            val bitmap = Bitmap.createBitmap(
                screenWidth + rowPadding / pixelStride,
                screenHeight,
                Bitmap.Config.ARGB_8888
            )
            
            bitmap.copyPixelsFromBuffer(buffer)
            
            if (rowPadding == 0) {
                bitmap
            } else {
                // 裁剪掉padding
                val croppedBitmap = Bitmap.createBitmap(bitmap, 0, 0, screenWidth, screenHeight)
                bitmap.recycle()
                croppedBitmap
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error converting image to bitmap", e)
            null
        }
    }
    
    /**
     * 压缩Bitmap为JPEG
     */
    private fun compressBitmapToJpeg(bitmap: Bitmap, quality: Int): ByteArray {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
        return outputStream.toByteArray()
    }
    
    /**
     * 发送屏幕数据到PC端
     */
    private fun sendScreenData(base64Data: String, width: Int, height: Int) {
        val screenData = ScreenDataMessage.ScreenData(
            imageData = base64Data,
            width = width,
            height = height,
            format = "JPEG",
            quality = JPEG_QUALITY
        )
        
        val message = ScreenDataMessage(screenData)
        networkClient?.sendMessage(message)
    }

    /**
     * 从服务请求屏幕录制权限
     */
    private fun requestScreenCapturePermissionFromService() {
        try {
            val intent = Intent(this, com.remoteandroid.control.ui.activities.MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            intent.putExtra("REQUEST_SCREEN_CAPTURE", true)
            startActivity(intent)

            Log.d(TAG, "从ScreenCaptureService启动MainActivity申请权限")

            // 停止当前服务，等待权限获得后重新启动
            stopSelf()

        } catch (e: Exception) {
            Log.e(TAG, "启动权限申请失败", e)
            stopSelf()
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "屏幕录制服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "RemoteAndroid屏幕录制服务正在运行"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("RemoteAndroid 屏幕录制")
            .setContentText("正在录制屏幕内容...")
            .setSmallIcon(R.drawable.ic_screen_share)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
}
