C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.exe
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.deps.json
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.runtimeconfig.json
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.pdb
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\CommunityToolkit.Mvvm.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\MainWindow.g.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\PasswordRecorderView.g.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\ScreenControlView.g.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\App.g.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF_MarkupCompile.cache
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF_MarkupCompile.lref
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\MainWindow.baml
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\PasswordRecorderView.baml
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\ScreenControlView.baml
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.g.resources
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.AssemblyInfo.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAn.F1E95C3F.Up2Date
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\refint\RemoteAndroidControl.WPF.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.pdb
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.genruntimeconfig.cache
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\ref\RemoteAndroidControl.WPF.dll
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Dialogs\ConnectionDialog.baml
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Dialogs\ConnectionDialog.g.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\FileManagerView.g.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\FileManagerView.baml
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\ApkBuilderView.g.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\ApkBuilderView.baml
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\GeneratedInternalTypeHelper.g.cs
C:\Users\<USER>\Desktop\测试项目2\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\App.baml
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.exe
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.deps.json
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.runtimeconfig.json
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\RemoteAndroidControl.WPF.pdb
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\CommunityToolkit.Mvvm.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Dialogs\ConnectionDialog.baml
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\MainWindow.g.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Dialogs\ConnectionDialog.g.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\ApkBuilderView.g.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\FileManagerView.g.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\PasswordRecorderView.g.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\ScreenControlView.g.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\App.g.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\GeneratedInternalTypeHelper.g.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF_MarkupCompile.cache
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF_MarkupCompile.lref
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\App.baml
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\MainWindow.baml
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\ApkBuilderView.baml
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\FileManagerView.baml
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\PasswordRecorderView.baml
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\Views\Modules\ScreenControlView.baml
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.g.resources
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.AssemblyInfo.cs
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAn.F1E95C3F.Up2Date
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\refint\RemoteAndroidControl.WPF.dll
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.pdb
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\RemoteAndroidControl.WPF.genruntimeconfig.cache
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\PC_Client\RemoteAndroidControl.WPF\RemoteAndroidControl.WPF\obj\Debug\net6.0-windows\ref\RemoteAndroidControl.WPF.dll
