http://schemas.android.com/apk/res-auto;;${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_primary.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_secondary.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_screen_share.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/button_outline.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/drawable/ic_notification.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher_round.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/mipmap-hdpi/ic_launcher.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/strings.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/values/themes.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/accessibility_service_config.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/data_extraction_rules.xml,${\:app*release*MAIN*sourceProvider*0*resDir*0}/xml/backup_rules.xml,+color:colorPrimary,0,V4000b0185,30000b01b1,;"#FF2196F3";button_primary,0,V400200475,32002004a3,;"#FF2196F3";purple_200,0,V400020037,2e00020061,;"#FFBB86FC";colorPrimaryDark,0,V4000c01b6,34000c01e6,;"#FF1976D2";backgroundColor,0,V40010022e,330010025d,;"#FFF5F5F5";text_primary,0,V400180384,30001803b0,;"#FF212121";textColorPrimary,0,V400160318,**********,;"#FF212121";black,0,V40007011e,**********,;"#FF000000";card_background,0,V4001302d1,**********,;"#FFFFFFFF";button_primary_pressed,0,V4002104a8,3a002104de,;"#FF1976D2";button_secondary,0,V4002204e3,**********,;"#FFF5F5F5";teal_200,0,V4000500c4,2c000500ec,;"#FF03DAC5";accent_secondary,0,V4001d042d,34001d045d,;"#FF03DAC5";colorAccent,0,V4000d01eb,2f000d0216,;"#FF03DAC5";cardBackgroundColor,0,V400110262,**********,;"#FFFFFFFF";textColorSecondary,0,V40017034d,360017037f,;"#FF757575";accent_primary,0,V4001c03fa,32001c0428,;"#FF2196F3";white,0,V400080148,290008016d,;"#FFFFFFFF";teal_700,0,V4000600f1,2c00060119,;"#FF018786";purple_700,0,V400040095,2e000400bf,;"#FF3700B3";background_primary,0,V40012029a,36001202cc,;"#FFF8F9FA";purple_500,0,V400030066,2e00030090,;"#FF6200EE";button_outline,0,V400230518,3200230546,;"#00000000";text_secondary,0,V4001903b5,32001903e3,;"#FF757575";+drawable:button_primary,1,F;button_secondary,2,F;ic_screen_share,3,F;button_outline,4,F;ic_notification,5,F;+id:tv_permission_title,6,F;tv_title,7,F;tv_permission_description,6,F;ll_steps_container,6,F;tv_status,7,F;btn_permission_guide,7,F;btn_request_permission,6,F;btn_skip,6,F;tv_permission_reason,6,F;btn_next,6,F;+layout:activity_main,7,F;activity_permission_guide,6,F;+mipmap:ic_launcher_round,8,F;ic_launcher,9,F;+string:app_name,10,V400010010,3a00010046,;"RemoteAndroid Control";accessibility_service_description,10,V40002004b,5d000200a4,;"RemoteAndroid远程控制服务，用于密码记录和远程操作";+style:Theme.RemoteAndroidControl,11,V400020064,c000e0326,;DTheme.Material3.DayNight,colorPrimary:@color/purple_500,colorPrimaryVariant:@color/purple_700,colorOnPrimary:@color/white,colorSecondary:@color/teal_200,colorSecondaryVariant:@color/teal_700,colorOnSecondary:@color/black,android\:statusBarColor:?attr/colorPrimaryVariant,;+xml:accessibility_service_config,12,F;data_extraction_rules,13,F;backup_rules,14,F;