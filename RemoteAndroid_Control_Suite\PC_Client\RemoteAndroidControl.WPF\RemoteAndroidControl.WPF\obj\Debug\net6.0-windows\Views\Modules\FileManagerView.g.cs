﻿#pragma checksum "..\..\..\..\..\Views\Modules\FileManagerView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DC3D890C6F994BF4B921C17D6F6E43D17E57F3F4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using RemoteAndroidControl.WPF.Views.Modules;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RemoteAndroidControl.WPF.Views.Modules {
    
    
    /// <summary>
    /// FileManagerView
    /// </summary>
    public partial class FileManagerView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnNavigateUp;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCreateFolder;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnUpload;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSearch;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearSearch;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkIncludeHidden;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbSortBy;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkSortAscending;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid FileListGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RemoteAndroidControl.WPF;component/views/modules/filemanagerview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Modules\FileManagerView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnNavigateUp = ((System.Windows.Controls.Button)(target));
            return;
            case 2:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            return;
            case 3:
            this.BtnCreateFolder = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.BtnUpload = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.BtnSearch = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.BtnClearSearch = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.ChkIncludeHidden = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.CmbSortBy = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.ChkSortAscending = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.FileListGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

