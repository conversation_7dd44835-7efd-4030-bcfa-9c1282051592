-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:2:1-87:12
INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:2:1-87:12
INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:2:1-87:12
INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:2:1-87:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d5087baff1a4001a5b77a578bce78ed\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\15b6801619e19352a8153d6cd8d21d93\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66302ff2f3a3107057e6c3d9f22160c6\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a1370fee3db5402869b59d38e9f7680e\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abd02ae003b9a084faf6627ffaa09c85\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b05dec457a862c59b6f8ef26eb1adaa9\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d657f96119f1b149b1b32d211b298c31\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3704603e351687715c5d03731bee439c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9805eda9c9e54f0360f0b3fbbd9120a9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fe94c3e4ea1962681cffc229becd8308\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1235315be7d31bce1aa8b5bfb2d1108a\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\49a4a3ea08186169c3cbe63cf6d65be6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd2ebcc34f43bb33a5db308c4d964387\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfb9b1add6789b04a0c20673abfe5c95\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e61923ec68a6c1a405ebd949b203377e\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c080bf805c0c26b5ce95981ea400c29\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4e86149e55e8994da12c4b5f858fca43\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e9991f450fd94b073902025e7b084cc3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5417f1cdb476daf5017a50a2a8cef1af\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a7f424c96120e11d86cb7e228d37016d\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\20211e3aa026e7c5d34ee907e6322afa\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d35c5093cb170b93636a303cf46f35a\transformed\ui-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-text:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d3fe0ebf83f4cfe9587536ec2d2777db\transformed\ui-text-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ead76b6db0ce221881201d06a718423b\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2c8483987ea398a7c2dbefacc9de34ca\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ebb3ee5a014387bb8e7bdf54510492e9\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a48e2326abb99d638d9a64fa169ff28\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\369ad63744ce12643b36563986ca4896\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fb46c77f559e9c5750248893094fdf83\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0d4d84cca3a2851a84947dee7f001deb\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\40caa30ef31ec89a6fba3f6bd3f80313\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4c4774f4a5bbeec382476bddb8a4d3\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d8630f4331e6c03ca3bad3f3e7347bc9\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e8f8b74dd45741093195f43c62592b91\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7da2721af625969f790476eb0def1809\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\88ba829332a27e62e552a269b7f8ad4e\transformed\runtime-saveable-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-graphics:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0467708ebc44962c8bfef45976c23665\transformed\ui-graphics-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-unit:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\91788e2e4c456097b4376f0231aea336\transformed\ui-unit-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.ui:ui-geometry:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbfdf61b5251a6cf3638ffb802d79dcd\transformed\ui-geometry-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\954bf05571d0072a3e34bb590dd89aca\transformed\runtime-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a783141b669ee0b01be4ad00aed144d8\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5be011737ad2ef977625a98329920c5d\transformed\ui-util-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a753afdce5c19543dcf31eca642a52f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ce2e56a93243105f7b758891e640685f\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\adfd87879060edf5666979e9eec9d4fa\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\364d623ea3f3a6523b4a382288eedc60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4e054bf8548234619f3a4896613a8917\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799e464f8672d9176a87fdb584d4e341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\63379045a48f3150c4e6bc99ab1fcefc\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7fc161b0756306b7a05bc2481b74e0bd\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c90d3631d89d8fade9f19c28641692ab\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\29534a18900a484bde87ea6077dab5b7\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:10:5-85
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:10:22-82
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:13:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:13:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:14:5-94
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:14:22-91
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:15:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:15:22-75
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:18:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:18:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:19:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:19:22-78
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:22:5-95
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:22:22-92
application
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:24:5-85:19
INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:24:5-85:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d5087baff1a4001a5b77a578bce78ed\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d5087baff1a4001a5b77a578bce78ed\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\15b6801619e19352a8153d6cd8d21d93\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\15b6801619e19352a8153d6cd8d21d93\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\364d623ea3f3a6523b4a382288eedc60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\364d623ea3f3a6523b4a382288eedc60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799e464f8672d9176a87fdb584d4e341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799e464f8672d9176a87fdb584d4e341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:31:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:29:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:27:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:30:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:33:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:28:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:25:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:32:9-58
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:26:9-65
activity#com.remoteandroid.control.ui.activities.MainActivity
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:36:9-44:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:38:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:39:13-62
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:37:13-55
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:40:13-43:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:41:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:41:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:42:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:42:27-74
activity#com.remoteandroid.control.ui.activities.PermissionGuideActivity
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:47:9-49:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:49:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:48:13-66
activity#com.remoteandroid.control.ui.activities.ScreenMaskActivity
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:52:9-54:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:54:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:53:13-61
service#com.remoteandroid.control.services.core.RemoteAccessibilityService
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:57:9-67:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:59:13-37
	android:permission
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:60:13-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:58:13-69
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:61:13-63:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:62:17-92
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:62:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:64:13-66:72
	android:resource
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:66:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:65:17-60
service#com.remoteandroid.control.services.core.RemoteControlForegroundService
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:70:9-73:63
	android:exported
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:72:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:73:13-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:71:13-73
service#com.remoteandroid.control.services.core.ScreenCaptureService
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:76:9-78:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:78:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:77:13-63
service#com.remoteandroid.control.services.core.FileService
ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:81:9-83:40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:83:13-37
	android:name
		ADDED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:82:13-54
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d5087baff1a4001a5b77a578bce78ed\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d5087baff1a4001a5b77a578bce78ed\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\15b6801619e19352a8153d6cd8d21d93\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\15b6801619e19352a8153d6cd8d21d93\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66302ff2f3a3107057e6c3d9f22160c6\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66302ff2f3a3107057e6c3d9f22160c6\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a1370fee3db5402869b59d38e9f7680e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a1370fee3db5402869b59d38e9f7680e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abd02ae003b9a084faf6627ffaa09c85\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\abd02ae003b9a084faf6627ffaa09c85\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b05dec457a862c59b6f8ef26eb1adaa9\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\b05dec457a862c59b6f8ef26eb1adaa9\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d657f96119f1b149b1b32d211b298c31\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d657f96119f1b149b1b32d211b298c31\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3704603e351687715c5d03731bee439c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\3704603e351687715c5d03731bee439c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9805eda9c9e54f0360f0b3fbbd9120a9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\9805eda9c9e54f0360f0b3fbbd9120a9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fe94c3e4ea1962681cffc229becd8308\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fe94c3e4ea1962681cffc229becd8308\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1235315be7d31bce1aa8b5bfb2d1108a\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\1235315be7d31bce1aa8b5bfb2d1108a\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\49a4a3ea08186169c3cbe63cf6d65be6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\49a4a3ea08186169c3cbe63cf6d65be6\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd2ebcc34f43bb33a5db308c4d964387\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fd2ebcc34f43bb33a5db308c4d964387\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfb9b1add6789b04a0c20673abfe5c95\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cfb9b1add6789b04a0c20673abfe5c95\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e61923ec68a6c1a405ebd949b203377e\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e61923ec68a6c1a405ebd949b203377e\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c080bf805c0c26b5ce95981ea400c29\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8c080bf805c0c26b5ce95981ea400c29\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4e86149e55e8994da12c4b5f858fca43\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4e86149e55e8994da12c4b5f858fca43\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e9991f450fd94b073902025e7b084cc3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e9991f450fd94b073902025e7b084cc3\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5417f1cdb476daf5017a50a2a8cef1af\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5417f1cdb476daf5017a50a2a8cef1af\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a7f424c96120e11d86cb7e228d37016d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a7f424c96120e11d86cb7e228d37016d\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\20211e3aa026e7c5d34ee907e6322afa\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\20211e3aa026e7c5d34ee907e6322afa\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d35c5093cb170b93636a303cf46f35a\transformed\ui-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2d35c5093cb170b93636a303cf46f35a\transformed\ui-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-text:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d3fe0ebf83f4cfe9587536ec2d2777db\transformed\ui-text-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-text:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d3fe0ebf83f4cfe9587536ec2d2777db\transformed\ui-text-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ead76b6db0ce221881201d06a718423b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ead76b6db0ce221881201d06a718423b\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2c8483987ea398a7c2dbefacc9de34ca\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\2c8483987ea398a7c2dbefacc9de34ca\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ebb3ee5a014387bb8e7bdf54510492e9\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ebb3ee5a014387bb8e7bdf54510492e9\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a48e2326abb99d638d9a64fa169ff28\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a48e2326abb99d638d9a64fa169ff28\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\369ad63744ce12643b36563986ca4896\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\369ad63744ce12643b36563986ca4896\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fb46c77f559e9c5750248893094fdf83\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\fb46c77f559e9c5750248893094fdf83\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0d4d84cca3a2851a84947dee7f001deb\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0d4d84cca3a2851a84947dee7f001deb\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\40caa30ef31ec89a6fba3f6bd3f80313\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\40caa30ef31ec89a6fba3f6bd3f80313\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4c4774f4a5bbeec382476bddb8a4d3\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7d4c4774f4a5bbeec382476bddb8a4d3\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d8630f4331e6c03ca3bad3f3e7347bc9\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d8630f4331e6c03ca3bad3f3e7347bc9\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e8f8b74dd45741093195f43c62592b91\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\e8f8b74dd45741093195f43c62592b91\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7da2721af625969f790476eb0def1809\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7da2721af625969f790476eb0def1809\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\88ba829332a27e62e552a269b7f8ad4e\transformed\runtime-saveable-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\88ba829332a27e62e552a269b7f8ad4e\transformed\runtime-saveable-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-graphics:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0467708ebc44962c8bfef45976c23665\transformed\ui-graphics-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-graphics:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\0467708ebc44962c8bfef45976c23665\transformed\ui-graphics-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-unit:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\91788e2e4c456097b4376f0231aea336\transformed\ui-unit-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-unit:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\91788e2e4c456097b4376f0231aea336\transformed\ui-unit-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-geometry:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbfdf61b5251a6cf3638ffb802d79dcd\transformed\ui-geometry-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-geometry:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\cbfdf61b5251a6cf3638ffb802d79dcd\transformed\ui-geometry-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\954bf05571d0072a3e34bb590dd89aca\transformed\runtime-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\954bf05571d0072a3e34bb590dd89aca\transformed\runtime-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a783141b669ee0b01be4ad00aed144d8\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\a783141b669ee0b01be4ad00aed144d8\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5be011737ad2ef977625a98329920c5d\transformed\ui-util-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.ui:ui-util:1.0.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\5be011737ad2ef977625a98329920c5d\transformed\ui-util-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a753afdce5c19543dcf31eca642a52f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\8a753afdce5c19543dcf31eca642a52f\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ce2e56a93243105f7b758891e640685f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\ce2e56a93243105f7b758891e640685f\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\adfd87879060edf5666979e9eec9d4fa\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\adfd87879060edf5666979e9eec9d4fa\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\364d623ea3f3a6523b4a382288eedc60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\364d623ea3f3a6523b4a382288eedc60\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4e054bf8548234619f3a4896613a8917\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\4e054bf8548234619f3a4896613a8917\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799e464f8672d9176a87fdb584d4e341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799e464f8672d9176a87fdb584d4e341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\63379045a48f3150c4e6bc99ab1fcefc\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\63379045a48f3150c4e6bc99ab1fcefc\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7fc161b0756306b7a05bc2481b74e0bd\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\7fc161b0756306b7a05bc2481b74e0bd\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c90d3631d89d8fade9f19c28641692ab\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\c90d3631d89d8fade9f19c28641692ab\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\29534a18900a484bde87ea6077dab5b7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\29534a18900a484bde87ea6077dab5b7\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799e464f8672d9176a87fdb584d4e341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\799e464f8672d9176a87fdb584d4e341\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.remoteandroid.control.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.remoteandroid.control.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
