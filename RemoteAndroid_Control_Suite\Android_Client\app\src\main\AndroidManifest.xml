<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- 无障碍服务权限 -->
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
    
    <!-- 屏幕录制权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    
    <!-- 文件访问权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
    <!-- 电池优化权限 -->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.RemoteAndroidControl"
        tools:targetApi="31">
        
        <!-- 主Activity -->
        <activity
            android:name=".ui.activities.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.RemoteAndroidControl">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <!-- 权限引导Activity -->
        <activity
            android:name=".ui.activities.PermissionGuideActivity"
            android:exported="false" />
            
        <!-- 屏幕遮蔽Activity -->
        <activity
            android:name=".ui.activities.ScreenMaskActivity"
            android:exported="false" />

        <!-- 无障碍服务 -->
        <service
            android:name=".services.core.RemoteAccessibilityService"
            android:exported="false"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- 前台服务 -->
        <service
            android:name=".services.core.RemoteControlForegroundService"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <!-- 屏幕录制服务 -->
        <service
            android:name=".services.core.ScreenCaptureService"
            android:exported="false" />

        <!-- 文件服务 -->
        <service
            android:name=".services.core.FileService"
            android:exported="false" />

    </application>

</manifest>
