package com.remoteandroid.control.services.core

import android.app.*
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.remoteandroid.control.R
import com.remoteandroid.control.constants.AppConstants
import com.remoteandroid.control.constants.NetworkConstants
import com.remoteandroid.control.managers.PermissionManager
import com.remoteandroid.control.services.network.NetworkClient
import com.remoteandroid.control.ui.activities.MainActivity
import com.remoteandroid.control.utils.PermissionHelper
import com.remoteandroid.control.models.network.MessageBase
import com.remoteandroid.control.models.network.ScreenCaptureRequestMessage
import com.remoteandroid.control.models.network.ScreenCaptureResponseMessage
import com.remoteandroid.control.models.network.ScreenCaptureResponseData
import kotlinx.coroutines.*

/**
 * 远程控制前台服务
 * 核心功能：
 * 1. 保持应用在后台运行，防止被系统杀死
 * 2. 管理屏幕录制服务和无障碍服务
 * 3. 维护与PC端的网络连接
 * 4. 提供服务状态通知
 */
class RemoteControlForegroundService : Service(), NetworkClient.MessageListener {
    
    companion object {
        private const val TAG = "RemoteControlForegroundService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "remote_control_channel"
        private const val CHANNEL_NAME = "远程控制服务"
        
        // 服务状态
        private var isServiceRunning = false
        private var serviceInstance: RemoteControlForegroundService? = null
        
        fun isRunning(): Boolean = isServiceRunning
        fun getInstance(): RemoteControlForegroundService? = serviceInstance
        
        /**
         * 启动前台服务
         */
        fun startService(context: Context) {
            val intent = Intent(context, RemoteControlForegroundService::class.java)
            intent.action = "START_SERVICE"
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        /**
         * 停止前台服务
         */
        fun stopService(context: Context) {
            val intent = Intent(context, RemoteControlForegroundService::class.java)
            intent.action = "STOP_SERVICE"
            context.startService(intent)
        }
    }
    
    private lateinit var networkClient: NetworkClient
    private lateinit var permissionManager: PermissionManager
    private var screenCaptureService: ScreenCaptureService? = null

    // 屏幕录制权限数据
    private var screenCaptureResultCode: Int = -1
    private var screenCaptureResultData: Intent? = null

    private val serviceScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private var statusUpdateJob: Job? = null
    
    override fun onCreate() {
        super.onCreate()
        serviceInstance = this
        
        Log.d(TAG, "RemoteControlForegroundService 创建")
        
        // 创建通知渠道
        createNotificationChannel()
        
        // 初始化组件
        networkClient = NetworkClient.getInstance()
        permissionManager = PermissionManager(this)
        
        // 启动前台通知
        startForeground(NOTIFICATION_ID, createNotification("服务启动中..."))
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        val action = intent?.action

        when (action) {
            "START_SERVICE" -> {
                // 获取屏幕录制权限数据
                screenCaptureResultCode = intent?.getIntExtra("SCREEN_CAPTURE_RESULT_CODE", -1) ?: -1
                screenCaptureResultData = intent?.getParcelableExtra("SCREEN_CAPTURE_RESULT_DATA")

                startRemoteControlService()
            }
            "STOP_SERVICE" -> {
                stopRemoteControlService()
                return START_NOT_STICKY
            }
            "SCREEN_CAPTURE_PERMISSION_GRANTED" -> {
                // 屏幕录制权限已获得
                screenCaptureResultCode = intent?.getIntExtra("SCREEN_CAPTURE_RESULT_CODE", -1) ?: -1
                screenCaptureResultData = intent?.getParcelableExtra("SCREEN_CAPTURE_RESULT_DATA")

                Log.d(TAG, "=== 屏幕录制权限已获得 ===")
                Log.d(TAG, "resultCode: $screenCaptureResultCode")
                Log.d(TAG, "resultData: $screenCaptureResultData")
                Log.d(TAG, "resultData extras: ${screenCaptureResultData?.extras}")

                // 验证权限数据有效性
                if (screenCaptureResultCode == Activity.RESULT_OK && screenCaptureResultData != null) {
                    Log.d(TAG, "✅ 权限数据有效，启动屏幕录制")
                    // 启动屏幕录制
                    startScreenCapture()
                    // 发送成功响应
                    sendScreenCaptureResponse(true, "屏幕录制已启动", "started")
                } else {
                    Log.e(TAG, "❌ 权限数据无效，无法启动屏幕录制")
                    Log.e(TAG, "resultCode: $screenCaptureResultCode, resultData: $screenCaptureResultData")
                    // 发送失败响应
                    sendScreenCaptureResponse(false, "权限数据无效，无法启动屏幕录制", "invalid_permission_data")
                }
            }
            "SCREEN_CAPTURE_PERMISSION_DENIED" -> {
                // 屏幕录制权限被拒绝
                sendScreenCaptureResponse(false, "用户拒绝了屏幕录制权限", "permission_denied")
            }
        }

        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null // 不支持绑定
    }
    
    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "RemoteControlForegroundService 销毁")
        
        isServiceRunning = false
        serviceInstance = null
        
        // 取消所有协程
        serviceScope.cancel()
        
        // 停止相关服务
        stopScreenCapture()
    }
    
    /**
     * 启动远程控制服务
     */
    private fun startRemoteControlService() {
        if (isServiceRunning) {
            Log.d(TAG, "服务已在运行")
            return
        }
        
        isServiceRunning = true
        Log.d(TAG, "启动远程控制服务")
        
        // 更新通知
        updateNotification("正在连接PC端...")
        
        // 设置消息监听器
        networkClient.setMessageListener(this)

        // 启动网络连接
        serviceScope.launch {
            try {
                networkClient.connect()
                updateNotification("已连接到PC端")
                
                // 启动状态监控
                startStatusMonitoring()

                // 不自动启动屏幕录制，等待PC端请求
                Log.d(TAG, "服务启动完成，等待PC端屏幕录制请求")
                
            } catch (e: Exception) {
                Log.e(TAG, "启动服务失败", e)
                updateNotification("连接失败，正在重试...")
                
                // 延迟重试
                delay(5000)
                if (isServiceRunning) {
                    startRemoteControlService()
                }
            }
        }
    }
    
    /**
     * 停止远程控制服务
     */
    private fun stopRemoteControlService() {
        Log.d(TAG, "停止远程控制服务")
        
        isServiceRunning = false
        
        // 停止状态监控
        statusUpdateJob?.cancel()
        
        // 断开网络连接
        serviceScope.launch {
            try {
                networkClient.disconnect()
            } catch (e: Exception) {
                Log.e(TAG, "断开连接失败", e)
            }
        }
        
        // 停止屏幕录制
        stopScreenCapture()
        
        // 停止服务
        stopForeground(true)
        stopSelf()
    }
    
    /**
     * 启动屏幕录制
     */
    private fun startScreenCapture() {
        try {
            // 验证权限数据
            if (screenCaptureResultCode != Activity.RESULT_OK || screenCaptureResultData == null) {
                Log.e(TAG, "❌ 无法启动屏幕录制：权限数据无效")
                Log.e(TAG, "resultCode: $screenCaptureResultCode, resultData: $screenCaptureResultData")
                updateNotification("屏幕录制启动失败：权限数据无效")
                return
            }

            // 启动屏幕录制服务
            val captureIntent = Intent(this, ScreenCaptureService::class.java)
            captureIntent.action = ScreenCaptureService.ACTION_START_CAPTURE

            // 传递真实权限数据
            captureIntent.putExtra(ScreenCaptureService.EXTRA_RESULT_CODE, screenCaptureResultCode)
            captureIntent.putExtra(ScreenCaptureService.EXTRA_RESULT_DATA, screenCaptureResultData)

            Log.d(TAG, "✅ 使用真实权限数据启动屏幕录制")
            Log.d(TAG, "resultCode: $screenCaptureResultCode")
            Log.d(TAG, "resultData: $screenCaptureResultData")

            startService(captureIntent)
            Log.d(TAG, "屏幕录制服务启动成功")
            updateNotification("屏幕录制已启动")
        } catch (e: Exception) {
            Log.e(TAG, "启动屏幕录制失败", e)
            updateNotification("屏幕录制启动失败")
        }
    }
    
    /**
     * 停止屏幕录制
     */
    private fun stopScreenCapture() {
        try {
            val captureIntent = Intent(this, ScreenCaptureService::class.java)
            captureIntent.action = ScreenCaptureService.ACTION_STOP_CAPTURE
            startService(captureIntent)

            screenCaptureService = null
            Log.d(TAG, "屏幕录制已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止屏幕录制失败", e)
        }
    }
    
    /**
     * 启动状态监控
     */
    private fun startStatusMonitoring() {
        statusUpdateJob = serviceScope.launch {
            while (isServiceRunning) {
                try {
                    // 检查服务状态
                    val status = getServiceStatus()
                    
                    // 更新通知
                    val statusText = buildString {
                        append("远程控制运行中")
                        if (status["networkConnected"] == true) {
                            append(" | 网络已连接")
                        }
                        if (status["screenCapturing"] == true) {
                            append(" | 屏幕录制中")
                        }
                        if (status["accessibilityEnabled"] == true) {
                            append(" | 无障碍已启用")
                        }
                    }
                    updateNotification(statusText)
                    
                    // 发送状态到PC端
                    networkClient.sendHeartbeat(status)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "状态监控失败", e)
                }
                
                // 每30秒更新一次
                delay(30000)
            }
        }
    }
    
    /**
     * 获取服务状态
     */
    private fun getServiceStatus(): Map<String, Any> {
        return mapOf(
            "serviceRunning" to isServiceRunning,
            "networkConnected" to networkClient.isConnected(),
            "screenCapturing" to ScreenCaptureService.isCapturingStatic(),
            "accessibilityEnabled" to PermissionHelper.hasAccessibilityPermission(this),
            "timestamp" to System.currentTimeMillis()
        )
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "远程控制服务状态通知"
                setShowBadge(false)
                setSound(null, null)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建通知
     */
    private fun createNotification(content: String): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            } else {
                PendingIntent.FLAG_UPDATE_CURRENT
            }
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("RemoteAndroid 远程控制")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_notification) // 需要添加图标资源
            .setContentIntent(pendingIntent)
            .setOngoing(true) // 不可滑动删除
            .setAutoCancel(false)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build()
    }
    
    /**
     * 更新通知内容
     */
    private fun updateNotification(content: String) {
        try {
            val notification = createNotification(content)
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.notify(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            Log.e(TAG, "更新通知失败", e)
        }
    }
    
    /**
     * 获取网络客户端
     */
    fun getNetworkClient(): NetworkClient = networkClient
    
    /**
     * 获取权限管理器
     */
    fun getPermissionManager(): PermissionManager = permissionManager
    
    /**
     * 重启屏幕录制
     */
    fun restartScreenCapture() {
        serviceScope.launch {
            stopScreenCapture()
            delay(1000)
            if (PermissionHelper.hasScreenCapturePermission()) {
                startScreenCapture()
            }
        }
    }

    /**
     * 实现MessageListener接口 - 处理接收到的消息
     */
    override fun onMessageReceived(message: MessageBase) {
        Log.d(TAG, "收到消息: ${message.messageType}")

        when (message.messageType) {
            NetworkConstants.MESSAGE_TYPE_REQUEST_SCREEN_CAPTURE -> {
                handleScreenCaptureRequest(message)
            }
            else -> {
                Log.d(TAG, "未处理的消息类型: ${message.messageType}")
            }
        }
    }

    /**
     * 处理屏幕录制请求
     */
    private fun handleScreenCaptureRequest(message: MessageBase) {
        try {
            // 解析屏幕录制请求消息
            val gson = com.google.gson.Gson()
            val requestMessage = gson.fromJson(gson.toJson(message), ScreenCaptureRequestMessage::class.java)

            Log.d(TAG, "处理屏幕录制请求: ${requestMessage.requestData.action}")

            when (requestMessage.requestData.action) {
                "start" -> {
                    // 启动屏幕录制权限申请
                    requestScreenCapturePermissionFromMainActivity()
                }
                "stop" -> {
                    // 停止屏幕录制
                    stopScreenCapture()
                    sendScreenCaptureResponse(true, "屏幕录制已停止", "stopped")
                }
                else -> {
                    sendScreenCaptureResponse(false, "不支持的操作: ${requestMessage.requestData.action}", "error")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理屏幕录制请求失败", e)
            sendScreenCaptureResponse(false, "处理请求失败: ${e.message}", "error")
        }
    }

    /**
     * 请求屏幕录制权限
     */
    private fun requestScreenCapturePermission() {
        try {
            // 启动MainActivity来申请屏幕录制权限
            val intent = Intent(this, MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            intent.putExtra("REQUEST_SCREEN_CAPTURE", true)
            startActivity(intent)

            // 发送权限申请中的响应
            sendScreenCaptureResponse(true, "正在申请屏幕录制权限，请在设备上确认", "permission_requesting")

        } catch (e: Exception) {
            Log.e(TAG, "启动权限申请失败", e)
            sendScreenCaptureResponse(false, "无法启动权限申请: ${e.message}", "error")
        }
    }

    /**
     * 发送屏幕录制响应消息
     */
    private fun sendScreenCaptureResponse(success: Boolean, message: String, status: String) {
        try {
            val responseData = ScreenCaptureResponseData(
                success = success,
                message = message,
                captureStatus = status
            )

            val responseMessage = ScreenCaptureResponseMessage(responseData)

            // 发送响应消息到PC端
            serviceScope.launch {
                networkClient.sendMessage(responseMessage)
            }

            Log.d(TAG, "发送屏幕录制响应: $message, 状态: $status")

        } catch (e: Exception) {
            Log.e(TAG, "发送屏幕录制响应失败", e)
        }
    }

    /**
     * 启动MainActivity申请屏幕录制权限
     */
    private fun requestScreenCapturePermissionFromMainActivity() {
        try {
            val intent = Intent(this, MainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            intent.putExtra("REQUEST_SCREEN_CAPTURE", true)
            startActivity(intent)

            Log.d(TAG, "启动MainActivity申请屏幕录制权限")

        } catch (e: Exception) {
            Log.e(TAG, "启动权限申请失败", e)
        }
    }
}
