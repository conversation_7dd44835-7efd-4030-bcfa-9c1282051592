using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using RemoteAndroidControl.WPF.ViewModels.Modules;
using RemoteAndroidControl.WPF.Services.Network;
using RemoteAndroidControl.WPF.Models.Network;
using RemoteAndroidControl.WPF.Utils;

namespace RemoteAndroidControl.WPF.Views.Modules
{
    /// <summary>
    /// ScreenControlView.xaml 的交互逻辑
    /// 屏幕控制视图 - 实现实时屏幕显示和远程控制功能
    /// </summary>
    public partial class ScreenControlView : UserControl
    {
        private ScreenControlViewModel viewModel = null!;
        private ConnectionManager connectionManager = null!;
        private DispatcherTimer performanceTimer = null!;

        // 性能监控
        private int frameCount = 0;
        private DateTime lastFpsUpdate = DateTime.Now;
        private DateTime lastFrameTime = DateTime.Now;

        // 屏幕参数
        private int androidScreenWidth = 0;
        private int androidScreenHeight = 0;
        private bool isCapturing = false;

        public ScreenControlView()
        {
            InitializeComponent();
            InitializeViewModel();
            InitializeServices();
            InitializePerformanceMonitor();
        }

        /// <summary>
        /// 初始化ViewModel
        /// </summary>
        private void InitializeViewModel()
        {
            viewModel = new ScreenControlViewModel();
            DataContext = viewModel;
        }

        /// <summary>
        /// 初始化服务
        /// </summary>
        private void InitializeServices()
        {
            connectionManager = ConnectionManager.Instance;

            // 订阅连接状态变化
            connectionManager.ClientConnected += OnClientConnected;
            connectionManager.ClientDisconnected += OnClientDisconnected;
            connectionManager.ScreenDataReceived += OnScreenDataReceived;
            connectionManager.ScreenCaptureResponseReceived += OnScreenCaptureResponseReceived;

            // 更新初始状态
            UpdateConnectionStatus();
        }

        /// <summary>
        /// 初始化性能监控
        /// </summary>
        private void InitializePerformanceMonitor()
        {
            performanceTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            performanceTimer.Tick += UpdatePerformanceInfo;
            performanceTimer.Start();
        }

        /// <summary>
        /// 客户端连接事件
        /// </summary>
        private void OnClientConnected(object sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateConnectionStatus();
                NoConnectionPanel.Visibility = Visibility.Collapsed;
                LoadingPanel.Visibility = Visibility.Visible;
            });
        }

        /// <summary>
        /// 客户端断开连接事件
        /// </summary>
        private void OnClientDisconnected(object sender, EventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                UpdateConnectionStatus();
                NoConnectionPanel.Visibility = Visibility.Visible;
                LoadingPanel.Visibility = Visibility.Collapsed;
                ScreenImage.Source = null;
                isCapturing = false;
                StopCaptureButton.IsEnabled = false;
                StartCaptureButton.IsEnabled = true;
            });
        }

        /// <summary>
        /// 接收屏幕数据事件
        /// </summary>
        private void OnScreenDataReceived(object sender, ScreenDataMessage message)
        {
            Dispatcher.Invoke(() =>
            {
                try
                {
                    // 解码Base64图像数据
                    var imageData = Convert.FromBase64String(message.Data.ImageData);

                    // 创建BitmapImage
                    var bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.StreamSource = new MemoryStream(imageData);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    bitmap.Freeze();

                    // 显示图像
                    ScreenImage.Source = bitmap;

                    // 更新屏幕参数
                    androidScreenWidth = message.Data.Width;
                    androidScreenHeight = message.Data.Height;

                    // 隐藏加载指示器
                    LoadingPanel.Visibility = Visibility.Collapsed;

                    // 更新性能计数
                    frameCount++;
                    lastFrameTime = DateTime.Now;

                    // 更新设备信息
                    if (message.Data.DeviceInfo != null)
                    {
                        DeviceInfoText.Text = $"({message.Data.DeviceInfo.DeviceModel} - Android {message.Data.DeviceInfo.AndroidVersion})";
                    }

                }
                catch (Exception ex)
                {
                    // 处理图像解码错误
                    System.Diagnostics.Debug.WriteLine($"Failed to decode screen image: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 屏幕录制响应事件处理
        /// </summary>
        private void OnScreenCaptureResponseReceived(object sender, ScreenCaptureResponseReceivedEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                if (e.Success)
                {
                    switch (e.CaptureStatus)
                    {
                        case "started":
                            DeviceInfoText.Text = "(屏幕录制已启动)";
                            LoadingPanel.Visibility = Visibility.Visible;
                            break;
                        case "stopped":
                            DeviceInfoText.Text = "(屏幕录制已停止)";
                            isCapturing = false;
                            StartCaptureButton.IsEnabled = true;
                            StopCaptureButton.IsEnabled = false;
                            ScreenImage.Source = null;
                            NoConnectionPanel.Visibility = Visibility.Visible;
                            LoadingPanel.Visibility = Visibility.Collapsed;
                            break;
                        case "permission_denied":
                            DeviceInfoText.Text = "(屏幕录制权限被拒绝)";
                            MessageBox.Show("用户拒绝了屏幕录制权限申请", "权限被拒绝", MessageBoxButton.OK, MessageBoxImage.Warning);
                            isCapturing = false;
                            StartCaptureButton.IsEnabled = true;
                            StopCaptureButton.IsEnabled = false;
                            LoadingPanel.Visibility = Visibility.Collapsed;
                            break;
                    }
                }
                else
                {
                    DeviceInfoText.Text = $"(录制失败: {e.Message})";
                    MessageBox.Show($"屏幕录制请求失败: {e.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    isCapturing = false;
                    StartCaptureButton.IsEnabled = true;
                    StopCaptureButton.IsEnabled = false;
                    LoadingPanel.Visibility = Visibility.Collapsed;
                }
            });
        }

        /// <summary>
        /// 更新连接状态
        /// </summary>
        private void UpdateConnectionStatus()
        {
            bool isConnected = connectionManager.IsConnected;

            ConnectionStatusIndicator.Fill = new SolidColorBrush(
                isConnected ? Colors.LimeGreen : Colors.Red);
            ConnectionStatusText.Text = isConnected ? "已连接" : "未连接";

            StartCaptureButton.IsEnabled = isConnected && !isCapturing;
        }

        /// <summary>
        /// 更新性能信息
        /// </summary>
        private void UpdatePerformanceInfo(object sender, EventArgs e)
        {
            var now = DateTime.Now;
            var elapsed = (now - lastFpsUpdate).TotalSeconds;

            if (elapsed >= 1.0)
            {
                // 计算FPS
                int fps = (int)(frameCount / elapsed);
                FpsText.Text = $"FPS: {fps}";

                // 计算延迟
                var delay = (now - lastFrameTime).TotalMilliseconds;
                DelayText.Text = $"延迟: {delay:F0}ms";

                // 更新分辨率
                if (androidScreenWidth > 0 && androidScreenHeight > 0)
                {
                    ResolutionText.Text = $"分辨率: {androidScreenWidth}x{androidScreenHeight}";
                }

                // 重置计数器
                frameCount = 0;
                lastFpsUpdate = now;
            }
        }

        /// <summary>
        /// 屏幕图像鼠标左键点击事件
        /// </summary>
        private void ScreenImage_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!connectionManager.IsConnected || androidScreenWidth == 0 || androidScreenHeight == 0)
                return;

            // 获取点击位置
            var position = e.GetPosition(ScreenImage);

            // 坐标映射：从WPF控件坐标转换为Android屏幕坐标
            var mappedPoint = MapCoordinates(position, ScreenImage.ActualWidth, ScreenImage.ActualHeight);

            // 发送点击消息
            connectionManager.SendClickAsync((int)mappedPoint.X, (int)mappedPoint.Y, "click");

            // 视觉反馈
            ShowClickFeedback(position);
        }

        /// <summary>
        /// 屏幕图像鼠标右键点击事件
        /// </summary>
        private void ScreenImage_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!connectionManager.IsConnected || androidScreenWidth == 0 || androidScreenHeight == 0)
                return;

            // 获取点击位置
            var position = e.GetPosition(ScreenImage);

            // 坐标映射
            var mappedPoint = MapCoordinates(position, ScreenImage.ActualWidth, ScreenImage.ActualHeight);

            // 发送长按消息
            connectionManager.SendClickAsync((int)mappedPoint.X, (int)mappedPoint.Y, "long_click");

            // 视觉反馈
            ShowClickFeedback(position, true);
        }

        /// <summary>
        /// 屏幕图像鼠标移动事件
        /// </summary>
        private void ScreenImage_MouseMove(object sender, MouseEventArgs e)
        {
            // 可以在这里实现鼠标悬停效果或拖拽功能
            // 暂时不实现，避免过多的网络消息
        }

        /// <summary>
        /// 坐标映射：从WPF控件坐标转换为Android屏幕坐标
        /// </summary>
        private Point MapCoordinates(Point wpfPoint, double wpfWidth, double wpfHeight)
        {
            // 计算缩放比例
            double scaleX = androidScreenWidth / wpfWidth;
            double scaleY = androidScreenHeight / wpfHeight;

            // 转换坐标
            double androidX = wpfPoint.X * scaleX;
            double androidY = wpfPoint.Y * scaleY;

            // 确保坐标在有效范围内
            androidX = Math.Max(0, Math.Min(androidX, androidScreenWidth - 1));
            androidY = Math.Max(0, Math.Min(androidY, androidScreenHeight - 1));

            return new Point(androidX, androidY);
        }

        /// <summary>
        /// 显示点击反馈效果
        /// </summary>
        private void ShowClickFeedback(Point position, bool isLongClick = false)
        {
            // 创建点击反馈圆圈
            var feedback = new System.Windows.Shapes.Ellipse
            {
                Width = isLongClick ? 40 : 20,
                Height = isLongClick ? 40 : 20,
                Fill = new SolidColorBrush(isLongClick ? Colors.Orange : Colors.LimeGreen) { Opacity = 0.6 },
                Stroke = new SolidColorBrush(isLongClick ? Colors.DarkOrange : Colors.Green),
                StrokeThickness = 2
            };

            // 设置位置
            Canvas.SetLeft(feedback, position.X - feedback.Width / 2);
            Canvas.SetTop(feedback, position.Y - feedback.Height / 2);

            // 添加到画布 (需要在XAML中添加Canvas)
            // 这里简化处理，实际项目中可以添加动画效果
        }

        /// <summary>
        /// 开始录制按钮点击事件
        /// </summary>
        private async void StartCaptureButton_Click(object sender, RoutedEventArgs e)
        {
            if (!connectionManager.IsConnected)
            {
                MessageBox.Show("请先连接Android设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // 发送屏幕录制请求到Android端
                var requestMessage = new ScreenCaptureRequestMessage();
                requestMessage.Data.Action = "start";

                await connectionManager.SendMessageAsync(requestMessage);

                // 更新UI状态
                isCapturing = true;
                StartCaptureButton.IsEnabled = false;
                StopCaptureButton.IsEnabled = true;
                LoadingPanel.Visibility = Visibility.Visible;

                // 更新设备信息显示
                DeviceInfoText.Text = "(请求屏幕录制权限...)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发送屏幕录制请求失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 停止录制按钮点击事件
        /// </summary>
        private async void StopCaptureButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 发送停止屏幕录制请求到Android端
                var requestMessage = new ScreenCaptureRequestMessage();
                requestMessage.Data.Action = "stop";

                await connectionManager.SendMessageAsync(requestMessage);

                // 更新UI状态
                isCapturing = false;
                StartCaptureButton.IsEnabled = true;
                StopCaptureButton.IsEnabled = false;

                ScreenImage.Source = null;
                NoConnectionPanel.Visibility = Visibility.Visible;
                LoadingPanel.Visibility = Visibility.Collapsed;

                // 更新设备信息显示
                DeviceInfoText.Text = "(已连接)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"发送停止录制请求失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 设置按钮点击事件
        /// </summary>
        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示屏幕控制设置对话框
            // 可以设置分辨率、帧率、压缩质量等参数
            MessageBox.Show("屏幕控制设置功能开发中...", "设置", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            performanceTimer?.Stop();

            if (connectionManager != null)
            {
                connectionManager.ClientConnected -= OnClientConnected;
                connectionManager.ClientDisconnected -= OnClientDisconnected;
                connectionManager.ScreenDataReceived -= OnScreenDataReceived;
            }
        }
    }
}
