<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.7.0" type="partial_results">
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnsafeImplicitIntentLaunch">
            <map id="actionsSent">
                    <map id="android.settings.ACCESSIBILITY_SETTINGS (used to start an activity)">
                        <location id="0"
                            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/managers/PermissionManager.kt"
                            line="144"
                            column="22"
                            startOffset="4787"
                            endLine="144"
                            endColumn="68"
                            endOffset="4833"/>
                    </map>
            </map>
    </map>
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.remoteandroid.control.ui.activities.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.accent_secondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="30"
            column="12"
            startOffset="1076"
            endLine="30"
            endColumn="35"
            endOffset="1099"/>
        <location id="R.color.backgroundColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="17"
            column="12"
            startOffset="565"
            endLine="17"
            endColumn="34"
            endOffset="587"/>
        <location id="R.color.cardBackgroundColor"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="18"
            column="12"
            startOffset="617"
            endLine="18"
            endColumn="38"
            endOffset="643"/>
        <location id="R.color.colorAccent"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="498"
            endLine="14"
            endColumn="30"
            endOffset="516"/>
        <location id="R.color.colorPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="12"
            column="12"
            startOffset="396"
            endLine="12"
            endColumn="31"
            endOffset="415"/>
        <location id="R.color.colorPrimaryDark"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="13"
            column="12"
            startOffset="445"
            endLine="13"
            endColumn="35"
            endOffset="468"/>
        <location id="R.color.purple_200"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="29"
            endOffset="79"/>
        <location id="R.color.textColorPrimary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="23"
            column="12"
            startOffset="799"
            endLine="23"
            endColumn="35"
            endOffset="822"/>
        <location id="R.color.textColorSecondary"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="24"
            column="12"
            startOffset="852"
            endLine="24"
            endColumn="37"
            endOffset="877"/>
        <entry
            name="model"
            string="attr[colorOnPrimary(R),colorPrimaryVariant(R)],color[button_outline(U),button_primary_pressed(U),accent_primary(U),button_primary(U),button_secondary(U),text_secondary(U),background_primary(U),text_primary(U),card_background(U),white(U),purple_200(D),purple_500(U),purple_700(U),teal_200(U),teal_700(U),black(U),colorPrimary(D),colorPrimaryDark(D),colorAccent(D),backgroundColor(D),cardBackgroundColor(D),textColorPrimary(D),textColorSecondary(D),accent_secondary(D)],drawable[button_outline(U),button_primary(U),button_secondary(U),ic_notification(U),ic_screen_share(U)],id[tv_title(D),tv_status(U),btn_permission_guide(U),tv_permission_title(U),tv_permission_description(U),tv_permission_reason(U),ll_steps_container(U),btn_request_permission(U),btn_skip(U),btn_next(U)],layout[activity_main(U),activity_permission_guide(U)],mipmap[ic_launcher(U),ic_launcher_round(U)],string[app_name(U),accessibility_service_description(U)],style[Theme_RemoteAndroidControl(U),Theme_Material3_DayNight(R)],xml[data_extraction_rules(U),backup_rules(U),accessibility_service_config(U)];1a^2^3^4,1b^3^5,1c^6^7,1e^0,29^8^2b^9^7^a^1b^b,2a^8^2b^9^7^a^4^1b^b^1c^1a,2f^30^d^e^b^f^10^11^1,33^2e;;;"/>
    </map>

</incidents>
