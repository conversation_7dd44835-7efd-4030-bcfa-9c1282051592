using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using RemoteAndroidControl.WPF.Models.Network;

namespace RemoteAndroidControl.WPF.Services.Network
{
    /// <summary>
    /// RemoteAndroid Control Suite - TCP服务器
    /// 监听8888端口，处理Android客户端连接和消息通信
    /// </summary>
    public class TcpServer : IDisposable
    {
        #region 字段和属性

        private readonly ILogger<TcpServer>? _logger;
        private TcpListener? _tcpListener;
        private TcpClient? _connectedClient;
        private NetworkStream? _networkStream;
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isRunning = false;
        private readonly object _lockObject = new object();

        /// <summary>
        /// 服务器端口 - 硬编码为8888
        /// </summary>
        public const int SERVER_PORT = 8888;

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning => _isRunning;

        /// <summary>
        /// 是否有客户端连接
        /// </summary>
        public bool HasClient => _connectedClient?.Connected == true;

        #endregion

        #region 事件

        /// <summary>
        /// 客户端连接事件
        /// </summary>
        public event EventHandler<ClientConnectedEventArgs>? ClientConnected;

        /// <summary>
        /// 客户端断开连接事件
        /// </summary>
        public event EventHandler<ClientDisconnectedEventArgs>? ClientDisconnected;

        /// <summary>
        /// 消息接收事件
        /// </summary>
        public event EventHandler<MessageReceivedEventArgs>? MessageReceived;

        /// <summary>
        /// 错误事件
        /// </summary>
        public event EventHandler<ErrorEventArgs>? ErrorOccurred;

        #endregion

        #region 构造函数

        public TcpServer(ILogger<TcpServer>? logger = null)
        {
            _logger = logger;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 启动TCP服务器
        /// </summary>
        /// <returns>启动任务</returns>
        public async Task StartAsync()
        {
            if (_isRunning)
            {
                _logger?.LogWarning("TCP服务器已在运行中");
                return;
            }

            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                _tcpListener = new TcpListener(IPAddress.Any, SERVER_PORT);
                _tcpListener.Start();
                _isRunning = true;

                _logger?.LogInformation($"TCP服务器已启动，监听端口: {SERVER_PORT}");

                // 开始监听客户端连接
                _ = Task.Run(ListenForClientsAsync, _cancellationTokenSource.Token);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "启动TCP服务器失败");
                OnErrorOccurred(new ErrorEventArgs($"启动TCP服务器失败: {ex.Message}"));
                throw;
            }
        }

        /// <summary>
        /// 停止TCP服务器
        /// </summary>
        /// <returns>停止任务</returns>
        public async Task StopAsync()
        {
            if (!_isRunning)
            {
                return;
            }

            try
            {
                _isRunning = false;
                _cancellationTokenSource?.Cancel();

                // 断开客户端连接
                DisconnectClient();

                // 停止监听
                _tcpListener?.Stop();

                _logger?.LogInformation("TCP服务器已停止");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "停止TCP服务器时发生错误");
                OnErrorOccurred(new ErrorEventArgs($"停止TCP服务器失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 发送消息到客户端
        /// </summary>
        /// <param name="message">消息对象</param>
        /// <returns>发送任务</returns>
        public async Task<bool> SendMessageAsync(MessageBase message)
        {
            if (!HasClient || _networkStream == null)
            {
                _logger?.LogWarning("没有连接的客户端，无法发送消息");
                return false;
            }

            try
            {
                string jsonMessage = message.ToJson();
                byte[] data = Encoding.UTF8.GetBytes(jsonMessage + "\n");
                
                await _networkStream.WriteAsync(data, 0, data.Length);
                await _networkStream.FlushAsync();

                _logger?.LogDebug($"发送消息: {message.MessageType}");
                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "发送消息失败");
                OnErrorOccurred(new ErrorEventArgs($"发送消息失败: {ex.Message}"));
                return false;
            }
        }

        /// <summary>
        /// 断开客户端连接
        /// </summary>
        public void DisconnectClient()
        {
            lock (_lockObject)
            {
                if (_connectedClient != null)
                {
                    var clientEndPoint = _connectedClient.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                    
                    _networkStream?.Close();
                    _connectedClient.Close();
                    
                    _networkStream = null;
                    _connectedClient = null;

                    OnClientDisconnected(new ClientDisconnectedEventArgs(clientEndPoint));
                    _logger?.LogInformation($"客户端已断开连接: {clientEndPoint}");
                }
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 监听客户端连接
        /// </summary>
        /// <returns>监听任务</returns>
        private async Task ListenForClientsAsync()
        {
            while (_isRunning && _tcpListener != null)
            {
                try
                {
                    if (_cancellationTokenSource?.Token.IsCancellationRequested == true)
                        break;

                    var tcpClient = await _tcpListener.AcceptTcpClientAsync();
                    
                    // 如果已有客户端连接，断开旧连接
                    if (HasClient)
                    {
                        DisconnectClient();
                    }

                    // 建立新连接
                    lock (_lockObject)
                    {
                        _connectedClient = tcpClient;
                        _networkStream = tcpClient.GetStream();
                    }

                    var clientEndPoint = tcpClient.Client.RemoteEndPoint?.ToString() ?? "Unknown";
                    OnClientConnected(new ClientConnectedEventArgs(clientEndPoint));
                    _logger?.LogInformation($"客户端已连接: {clientEndPoint}");

                    // 开始处理客户端消息
                    _ = Task.Run(() => HandleClientAsync(tcpClient), _cancellationTokenSource?.Token ?? CancellationToken.None);
                }
                catch (ObjectDisposedException)
                {
                    // 服务器已停止，正常退出
                    break;
                }
                catch (Exception ex)
                {
                    if (_isRunning)
                    {
                        _logger?.LogError(ex, "接受客户端连接时发生错误");
                        OnErrorOccurred(new ErrorEventArgs($"接受客户端连接失败: {ex.Message}"));
                    }
                }
            }
        }

        /// <summary>
        /// 处理客户端消息
        /// </summary>
        /// <param name="client">客户端</param>
        /// <returns>处理任务</returns>
        private async Task HandleClientAsync(TcpClient client)
        {
            var buffer = new byte[4096];
            var messageBuffer = new StringBuilder();

            try
            {
                while (client.Connected && _isRunning)
                {
                    if (_cancellationTokenSource?.Token.IsCancellationRequested == true)
                        break;

                    var stream = client.GetStream();
                    int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

                    if (bytesRead == 0)
                    {
                        // 客户端断开连接
                        break;
                    }

                    string receivedData = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    messageBuffer.Append(receivedData);

                    // 处理完整的消息（以换行符分隔）
                    string bufferContent = messageBuffer.ToString();
                    string[] messages = bufferContent.Split('\n');

                    for (int i = 0; i < messages.Length - 1; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(messages[i]))
                        {
                            ProcessMessage(messages[i].Trim());
                        }
                    }

                    // 保留最后一个不完整的消息
                    messageBuffer.Clear();
                    if (!string.IsNullOrWhiteSpace(messages[messages.Length - 1]))
                    {
                        messageBuffer.Append(messages[messages.Length - 1]);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "处理客户端消息时发生错误");
                OnErrorOccurred(new ErrorEventArgs($"处理客户端消息失败: {ex.Message}"));
            }
            finally
            {
                DisconnectClient();
            }
        }

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        /// <param name="messageJson">JSON消息</param>
        private void ProcessMessage(string messageJson)
        {
            try
            {
                Console.WriteLine($"[DEBUG] 接收到原始消息: {messageJson.Substring(0, Math.Min(200, messageJson.Length))}...");

                var baseMessage = MessageBase.FromJson<MessageBase>(messageJson);
                if (baseMessage != null)
                {
                    Console.WriteLine($"[DEBUG] 解析成功，消息类型: {baseMessage.MessageType}");
                    OnMessageReceived(new MessageReceivedEventArgs(baseMessage, messageJson));
                    _logger?.LogDebug($"接收到消息: {baseMessage.MessageType}");
                }
                else
                {
                    Console.WriteLine("[DEBUG] 消息解析失败，baseMessage为null");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] 解析消息异常: {ex.Message}");
                _logger?.LogError(ex, $"解析消息失败: {messageJson}");
                OnErrorOccurred(new ErrorEventArgs($"解析消息失败: {ex.Message}"));
            }
        }

        #endregion

        #region 事件触发方法

        protected virtual void OnClientConnected(ClientConnectedEventArgs e)
        {
            ClientConnected?.Invoke(this, e);
        }

        protected virtual void OnClientDisconnected(ClientDisconnectedEventArgs e)
        {
            ClientDisconnected?.Invoke(this, e);
        }

        protected virtual void OnMessageReceived(MessageReceivedEventArgs e)
        {
            MessageReceived?.Invoke(this, e);
        }

        protected virtual void OnErrorOccurred(ErrorEventArgs e)
        {
            ErrorOccurred?.Invoke(this, e);
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            StopAsync().Wait();
            _cancellationTokenSource?.Dispose();
            _networkStream?.Dispose();
            _connectedClient?.Dispose();
            _tcpListener?.Stop();
        }

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 客户端连接事件参数
    /// </summary>
    public class ClientConnectedEventArgs : EventArgs
    {
        public string ClientEndPoint { get; }

        public ClientConnectedEventArgs(string clientEndPoint)
        {
            ClientEndPoint = clientEndPoint;
        }
    }

    /// <summary>
    /// 客户端断开连接事件参数
    /// </summary>
    public class ClientDisconnectedEventArgs : EventArgs
    {
        public string ClientEndPoint { get; }

        public ClientDisconnectedEventArgs(string clientEndPoint)
        {
            ClientEndPoint = clientEndPoint;
        }
    }

    /// <summary>
    /// 消息接收事件参数
    /// </summary>
    public class MessageReceivedEventArgs : EventArgs
    {
        public MessageBase Message { get; }
        public string RawJson { get; }

        public MessageReceivedEventArgs(MessageBase message, string rawJson)
        {
            Message = message;
            RawJson = rawJson;
        }
    }

    /// <summary>
    /// 错误事件参数
    /// </summary>
    public class ErrorEventArgs : EventArgs
    {
        public string ErrorMessage { get; }

        public ErrorEventArgs(string errorMessage)
        {
            ErrorMessage = errorMessage;
        }
    }

    #endregion
}
