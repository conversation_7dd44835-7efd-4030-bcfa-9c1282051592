{"logs": [{"outputFile": "com.remoteandroid.control.app-mergeReleaseResources-43:/values-es/values-es.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\46addebd337d7ba2893b2181119dd40d\\transformed\\core-1.12.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "38,39,40,41,42,43,44,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3503,3602,3704,3804,3902,4009,4115,10371", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3597,3699,3799,3897,4004,4110,4230,10467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\2d35c5093cb170b93636a303cf46f35a\\transformed\\ui-1.0.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,283,381,460,557,649,736,809,879,956,1026", "endColumns": "95,81,97,78,96,91,86,72,69,76,69,120", "endOffsets": "196,278,376,455,552,644,731,804,874,951,1021,1142"}, "to": {"startLines": "48,49,50,54,55,112,113,114,115,118,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4542,4638,4720,5059,5138,9806,9898,9985,10058,10294,10472,10542", "endColumns": "95,81,97,78,96,91,86,72,69,76,69,120", "endOffsets": "4633,4715,4813,5133,5230,9893,9980,10053,10123,10366,10537,10658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\a1370fee3db5402869b59d38e9f7680e\\transformed\\appcompat-1.6.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,320,428,513,614,742,828,909,1001,1095,1192,1286,1386,1480,1576,1672,1764,1856,1938,2045,2156,2255,2363,2471,2578,2737,2836", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "202,315,423,508,609,737,823,904,996,1090,1187,1281,1381,1475,1571,1667,1759,1851,1933,2040,2151,2250,2358,2466,2573,2732,2831,2914"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "328,430,543,651,736,837,965,1051,1132,1224,1318,1415,1509,1609,1703,1799,1895,1987,2079,2161,2268,2379,2478,2586,2694,2801,2960,10211", "endColumns": "101,112,107,84,100,127,85,80,91,93,96,93,99,93,95,95,91,91,81,106,110,98,107,107,106,158,98,82", "endOffsets": "425,538,646,731,832,960,1046,1127,1219,1313,1410,1504,1604,1698,1794,1890,1982,2074,2156,2263,2374,2473,2581,2689,2796,2955,3054,10289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7d5087baff1a4001a5b77a578bce78ed\\transformed\\material-1.11.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,278,359,438,525,626,722,826,948,1029,1094,1189,1270,1333,1422,1486,1555,1618,1692,1756,1812,1930,1988,2050,2106,2186,2325,2414,2496,2637,2718,2798,2949,3039,3119,3175,3231,3297,3376,3458,3546,3635,3709,3786,3856,3935,4035,4119,4203,4295,4395,4469,4550,4652,4705,4790,4857,4950,5039,5101,5165,5228,5296,5409,5516,5620,5721,5781,5841", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "273,354,433,520,621,717,821,943,1024,1089,1184,1265,1328,1417,1481,1550,1613,1687,1751,1807,1925,1983,2045,2101,2181,2320,2409,2491,2632,2713,2793,2944,3034,3114,3170,3226,3292,3371,3453,3541,3630,3704,3781,3851,3930,4030,4114,4198,4290,4390,4464,4545,4647,4700,4785,4852,4945,5034,5096,5160,5223,5291,5404,5511,5615,5716,5776,5836,5919"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,51,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3059,3140,3219,3306,3407,4235,4339,4461,4818,4883,4978,5235,5298,5387,5451,5520,5583,5657,5721,5777,5895,5953,6015,6071,6151,6290,6379,6461,6602,6683,6763,6914,7004,7084,7140,7196,7262,7341,7423,7511,7600,7674,7751,7821,7900,8000,8084,8168,8260,8360,8434,8515,8617,8670,8755,8822,8915,9004,9066,9130,9193,9261,9374,9481,9585,9686,9746,10128", "endLines": "5,33,34,35,36,37,45,46,47,51,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,116", "endColumns": "12,80,78,86,100,95,103,121,80,64,94,80,62,88,63,68,62,73,63,55,117,57,61,55,79,138,88,81,140,80,79,150,89,79,55,55,65,78,81,87,88,73,76,69,78,99,83,83,91,99,73,80,101,52,84,66,92,88,61,63,62,67,112,106,103,100,59,59,82", "endOffsets": "323,3135,3214,3301,3402,3498,4334,4456,4537,4878,4973,5054,5293,5382,5446,5515,5578,5652,5716,5772,5890,5948,6010,6066,6146,6285,6374,6456,6597,6678,6758,6909,6999,7079,7135,7191,7257,7336,7418,7506,7595,7669,7746,7816,7895,7995,8079,8163,8255,8355,8429,8510,8612,8665,8750,8817,8910,8999,9061,9125,9188,9256,9369,9476,9580,9681,9741,9801,10206"}}]}]}