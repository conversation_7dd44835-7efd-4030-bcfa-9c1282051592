package com.remoteandroid.control.models.network

import com.google.gson.annotations.SerializedName
import com.remoteandroid.control.constants.NetworkConstants

/**
 * RemoteAndroid Control Suite - 屏幕录制响应消息
 * Android端向PC端响应屏幕录制请求结果
 */
data class ScreenCaptureResponseMessage(
    val responseData: ScreenCaptureResponseData = ScreenCaptureResponseData()
) : MessageBase() {

    override val messageType: String = NetworkConstants.MESSAGE_TYPE_SCREEN_CAPTURE_RESPONSE
    override val data: Any = responseData
}

/**
 * 屏幕录制响应数据结构
 */
data class ScreenCaptureResponseData(
    @SerializedName("success")
    val success: Boolean = false,
    
    @SerializedName("message")
    val message: String = "",
    
    @SerializedName("errorCode")
    val errorCode: String? = null,
    
    @SerializedName("captureStatus")
    val captureStatus: String = "stopped" // started/stopped/permission_denied/error
)
