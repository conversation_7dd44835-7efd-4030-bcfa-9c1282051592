1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.remoteandroid.control"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 无障碍服务权限 -->
16    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
16-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:10:5-85
16-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:10:22-82
17
18    <!-- 屏幕录制权限 -->
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:13:5-77
19-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:13:22-74
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
20-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:14:5-94
20-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:14:22-91
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:15:5-78
21-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:15:22-75
22
23    <!-- 文件访问权限 -->
24    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:18:5-80
24-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:18:22-77
25    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:19:5-81
25-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:19:22-78
26
27    <!-- 电池优化权限 -->
28    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
28-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:22:5-95
28-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:22:22-92
29
30    <permission
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
31        android:name="com.remoteandroid.control.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.remoteandroid.control.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
35
36    <application
36-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:24:5-85:19
37        android:allowBackup="true"
37-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:25:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
39        android:dataExtractionRules="@xml/data_extraction_rules"
39-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:26:9-65
40        android:debuggable="true"
41        android:extractNativeLibs="false"
42        android:fullBackupContent="@xml/backup_rules"
42-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:27:9-54
43        android:icon="@mipmap/ic_launcher"
43-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:28:9-43
44        android:label="@string/app_name"
44-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:29:9-41
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:30:9-54
46        android:supportsRtl="true"
46-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:31:9-35
47        android:theme="@style/Theme.RemoteAndroidControl" >
47-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:32:9-58
48
49        <!-- 主Activity -->
50        <activity
50-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:36:9-44:20
51            android:name="com.remoteandroid.control.ui.activities.MainActivity"
51-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:37:13-55
52            android:exported="true"
52-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:38:13-36
53            android:theme="@style/Theme.RemoteAndroidControl" >
53-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:39:13-62
54            <intent-filter>
54-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:40:13-43:29
55                <action android:name="android.intent.action.MAIN" />
55-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:41:17-69
55-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:41:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:42:17-77
57-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:42:27-74
58            </intent-filter>
59        </activity>
60
61        <!-- 权限引导Activity -->
62        <activity
62-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:47:9-49:40
63            android:name="com.remoteandroid.control.ui.activities.PermissionGuideActivity"
63-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:48:13-66
64            android:exported="false" />
64-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:49:13-37
65
66        <!-- 屏幕遮蔽Activity -->
67        <activity
67-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:52:9-54:40
68            android:name="com.remoteandroid.control.ui.activities.ScreenMaskActivity"
68-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:53:13-61
69            android:exported="false" />
69-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:54:13-37
70
71        <!-- 无障碍服务 -->
72        <service
72-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:57:9-67:19
73            android:name="com.remoteandroid.control.services.core.RemoteAccessibilityService"
73-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:58:13-69
74            android:exported="false"
74-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:59:13-37
75            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
75-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:60:13-79
76            <intent-filter>
76-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:61:13-63:29
77                <action android:name="android.accessibilityservice.AccessibilityService" />
77-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:62:17-92
77-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:62:25-89
78            </intent-filter>
79
80            <meta-data
80-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:64:13-66:72
81                android:name="android.accessibilityservice"
81-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:65:17-60
82                android:resource="@xml/accessibility_service_config" />
82-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:66:17-69
83        </service>
84
85        <!-- 前台服务 -->
86        <service
86-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:70:9-73:63
87            android:name="com.remoteandroid.control.services.core.RemoteControlForegroundService"
87-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:71:13-73
88            android:exported="false"
88-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:72:13-37
89            android:foregroundServiceType="mediaProjection" />
89-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:73:13-60
90
91        <!-- 屏幕录制服务 -->
92        <service
92-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:76:9-78:40
93            android:name="com.remoteandroid.control.services.core.ScreenCaptureService"
93-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:77:13-63
94            android:exported="false" />
94-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:78:13-37
95
96        <!-- 文件服务 -->
97        <service
97-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:81:9-83:40
98            android:name="com.remoteandroid.control.services.core.FileService"
98-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:82:13-54
99            android:exported="false" />
99-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:83:13-37
100
101        <provider
101-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
102            android:name="androidx.startup.InitializationProvider"
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
103            android:authorities="com.remoteandroid.control.androidx-startup"
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
104            android:exported="false" >
104-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
105            <meta-data
105-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
106                android:name="androidx.emoji2.text.EmojiCompatInitializer"
106-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
107                android:value="androidx.startup" />
107-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
108            <meta-data
108-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
109                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
109-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
110                android:value="androidx.startup" />
110-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
111            <meta-data
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
112                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
113                android:value="androidx.startup" />
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
114        </provider>
115
116        <receiver
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
117            android:name="androidx.profileinstaller.ProfileInstallReceiver"
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
118            android:directBootAware="false"
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
119            android:enabled="true"
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
120            android:exported="true"
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
121            android:permission="android.permission.DUMP" >
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
122            <intent-filter>
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
123                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
124            </intent-filter>
125            <intent-filter>
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
126                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
126-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
127            </intent-filter>
128            <intent-filter>
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
129                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
129-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
130            </intent-filter>
131            <intent-filter>
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
132                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
132-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
133            </intent-filter>
134        </receiver>
135    </application>
136
137</manifest>
