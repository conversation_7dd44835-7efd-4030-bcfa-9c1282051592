package com.remoteandroid.control.models.network

import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import java.util.*

/**
 * RemoteAndroid Control Suite - 网络消息基类
 * 定义所有网络消息的通用结构和序列化方法
 */
abstract class MessageBase {

    // 消息类型 (子类必须实现)
    abstract val messageType: String

    // 消息ID (唯一标识) - 映射为"id"字段以匹配PC端
    @SerializedName("id")
    val messageId: String = UUID.randomUUID().toString()

    // 时间戳
    val timestamp: Long = System.currentTimeMillis()

    // 消息数据 (子类必须实现)
    abstract val data: Any?
    
    companion object {
        private val gson = Gson()
        
        /**
         * 将消息对象转换为JSON字符串
         */
        fun toJson(message: MessageBase): String {
            return gson.toJson(message)
        }
        
        /**
         * 从JSON字符串解析消息对象
         */
        fun <T : MessageBase> fromJson(json: String, clazz: Class<T>): T? {
            return try {
                gson.fromJson(json, clazz)
            } catch (e: Exception) {
                null
            }
        }
        
        /**
         * 从JSON字符串解析为通用消息对象
         */
        fun fromJson(json: String): MessageBase? {
            return try {
                gson.fromJson(json, MessageBase::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    /**
     * 将当前消息转换为JSON字符串
     */
    fun toJson(): String {
        return gson.toJson(this)
    }
    
    /**
     * 获取消息大小 (字节)
     */
    fun getMessageSize(): Int {
        return toJson().toByteArray().size
    }
    
    /**
     * 检查消息是否有效
     */
    open fun isValid(): Boolean {
        return messageType.isNotEmpty() && messageId.isNotEmpty() && timestamp > 0
    }
    
    /**
     * 获取消息摘要信息
     */
    fun getSummary(): String {
        return "MessageType: $messageType, ID: ${messageId.take(8)}..., Timestamp: $timestamp"
    }
    
    override fun toString(): String {
        return "MessageBase(messageType='$messageType', messageId='$messageId', timestamp=$timestamp)"
    }
    
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is MessageBase) return false
        
        return messageId == other.messageId
    }
    
    override fun hashCode(): Int {
        return messageId.hashCode()
    }
}
