using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using RemoteAndroidControl.WPF.Models.Network;

namespace RemoteAndroidControl.WPF.Services.Network
{
    /// <summary>
    /// RemoteAndroid Control Suite - 连接管理器
    /// 管理TCP服务器和消息处理的协调工作
    /// </summary>
    public class ConnectionManager : IDisposable
    {
        #region 单例模式

        private static ConnectionManager? _instance;
        private static readonly object _lock = new object();

        public static ConnectionManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new ConnectionManager();
                    }
                }
                return _instance;
            }
        }

        #endregion

        #region 字段和属性

        private readonly TcpServer _tcpServer;
        private readonly MessageHandler _messageHandler;
        private readonly ILogger<ConnectionManager>? _logger;

        /// <summary>
        /// 是否正在运行
        /// </summary>
        public bool IsRunning => _tcpServer.IsRunning;

        /// <summary>
        /// 是否有客户端连接
        /// </summary>
        public bool IsConnected => _tcpServer.HasClient;

        /// <summary>
        /// 服务器是否正在运行
        /// </summary>
        public bool IsServerRunning => _tcpServer.IsRunning;

        #endregion

        #region 事件

        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        public event EventHandler<ConnectionStatusChangedEventArgs>? ConnectionStatusChanged;

        /// <summary>
        /// 屏幕数据接收事件
        /// </summary>
        public event EventHandler<ScreenDataReceivedEventArgs>? ScreenDataReceivedDetailed;

        /// <summary>
        /// 错误事件
        /// </summary>
        public event EventHandler<ConnectionErrorEventArgs>? ErrorOccurred;

        /// <summary>
        /// 客户端连接事件
        /// </summary>
        public event EventHandler? ClientConnected;

        /// <summary>
        /// 消息接收事件
        /// </summary>
        public event EventHandler<MessageReceivedEventArgs>? MessageReceived;

        /// <summary>
        /// 客户端断开连接事件
        /// </summary>
        public event EventHandler? ClientDisconnected;

        /// <summary>
        /// 屏幕数据接收事件（简化版本）
        /// </summary>
        public event EventHandler<ScreenDataMessage>? ScreenDataReceived;

        /// <summary>
        /// 屏幕录制响应接收事件
        /// </summary>
        public event EventHandler<ScreenCaptureResponseReceivedEventArgs>? ScreenCaptureResponseReceived;

        #endregion

        #region 构造函数

        public ConnectionManager(ILogger<ConnectionManager>? logger = null)
        {
            _logger = logger;
            _tcpServer = new TcpServer();
            _messageHandler = new MessageHandler();

            // 绑定事件
            BindEvents();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 启动连接管理器
        /// </summary>
        /// <returns>启动任务</returns>
        public async Task StartAsync()
        {
            try
            {
                await _tcpServer.StartAsync();
                _logger?.LogInformation("连接管理器已启动");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "启动连接管理器失败");
                OnErrorOccurred(new ConnectionErrorEventArgs($"启动失败: {ex.Message}"));
                throw;
            }
        }

        /// <summary>
        /// 停止连接管理器
        /// </summary>
        /// <returns>停止任务</returns>
        public async Task StopAsync()
        {
            try
            {
                await _tcpServer.StopAsync();
                _logger?.LogInformation("连接管理器已停止");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "停止连接管理器时发生错误");
                OnErrorOccurred(new ConnectionErrorEventArgs($"停止失败: {ex.Message}"));
            }
        }

        /// <summary>
        /// 发送点击指令
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="action">动作类型</param>
        /// <returns>发送任务</returns>
        public async Task<bool> SendClickAsync(int x, int y, string action = "click")
        {
            var clickMessage = new ClickMessage
            {
                Data = new ClickData
                {
                    X = x,
                    Y = y,
                    Action = action,
                    Duration = action == "long_click" ? 1000 : 100
                }
            };

            return await _tcpServer.SendMessageAsync(clickMessage);
        }

        /// <summary>
        /// 发送滑动指令
        /// </summary>
        /// <param name="startX">起始X坐标</param>
        /// <param name="startY">起始Y坐标</param>
        /// <param name="endX">结束X坐标</param>
        /// <param name="endY">结束Y坐标</param>
        /// <param name="duration">持续时间</param>
        /// <returns>发送任务</returns>
        public async Task<bool> SendSwipeAsync(int startX, int startY, int endX, int endY, int duration = 300)
        {
            var swipeMessage = new ClickMessage
            {
                Data = new ClickData
                {
                    X = startX,
                    Y = startY,
                    EndX = endX,
                    EndY = endY,
                    Action = "swipe",
                    Duration = duration
                }
            };

            return await _tcpServer.SendMessageAsync(swipeMessage);
        }

        /// <summary>
        /// 发送心跳消息
        /// </summary>
        /// <returns>发送任务</returns>
        public async Task<bool> SendHeartbeatAsync()
        {
            var heartbeatMessage = new HeartbeatMessage();
            return await _tcpServer.SendMessageAsync(heartbeatMessage);
        }

        /// <summary>
        /// 发送屏幕录制请求
        /// </summary>
        /// <returns>发送任务</returns>
        public async Task<bool> SendScreenCaptureRequestAsync()
        {
            var requestMessage = new ScreenCaptureRequestMessage();
            return await _tcpServer.SendMessageAsync(requestMessage);
        }

        /// <summary>
        /// 断开客户端连接
        /// </summary>
        public void DisconnectClient()
        {
            _tcpServer.DisconnectClient();
        }

        /// <summary>
        /// 启动服务器
        /// </summary>
        public async void StartServer(string ipAddress, int port)
        {
            await _tcpServer.StartAsync();
        }

        /// <summary>
        /// 停止服务器
        /// </summary>
        public async void StopServer()
        {
            await _tcpServer.StopAsync();
        }

        /// <summary>
        /// 发送消息（通用方法）
        /// </summary>
        public async Task SendMessageAsync(MessageBase message)
        {
            if (!IsConnected)
                throw new InvalidOperationException("没有客户端连接");

            await _tcpServer.SendMessageAsync(message);
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 绑定事件
        /// </summary>
        private void BindEvents()
        {
            // TCP服务器事件
            _tcpServer.ClientConnected += OnClientConnected;
            _tcpServer.ClientDisconnected += OnClientDisconnected;
            _tcpServer.MessageReceived += OnMessageReceived;
            _tcpServer.ErrorOccurred += OnTcpServerError;

            // 消息处理器事件
            _messageHandler.ScreenDataReceived += OnScreenDataReceived;
            _messageHandler.HeartbeatReceived += OnHeartbeatReceived;
            _messageHandler.ConnectResponseReceived += OnConnectResponseReceived;
            _messageHandler.ScreenCaptureResponseReceived += OnScreenCaptureResponseReceived;
        }

        /// <summary>
        /// 客户端连接事件处理
        /// </summary>
        private void OnClientConnected(object? sender, ClientConnectedEventArgs e)
        {
            _logger?.LogInformation($"客户端已连接: {e.ClientEndPoint}");
            OnConnectionStatusChanged(new ConnectionStatusChangedEventArgs(true, e.ClientEndPoint));

            // 触发简化事件
            ClientConnected?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 客户端断开连接事件处理
        /// </summary>
        private void OnClientDisconnected(object? sender, ClientDisconnectedEventArgs e)
        {
            _logger?.LogInformation($"客户端已断开: {e.ClientEndPoint}");
            OnConnectionStatusChanged(new ConnectionStatusChangedEventArgs(false, e.ClientEndPoint));

            // 触发简化事件
            ClientDisconnected?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// 消息接收事件处理
        /// </summary>
        private async void OnMessageReceived(object? sender, MessageReceivedEventArgs e)
        {
            // 触发公共事件
            MessageReceived?.Invoke(this, e);

            // 处理消息
            await _messageHandler.HandleMessageAsync(e.Message, e.RawJson);
        }

        /// <summary>
        /// TCP服务器错误事件处理
        /// </summary>
        private void OnTcpServerError(object? sender, Services.Network.ErrorEventArgs e)
        {
            OnErrorOccurred(new ConnectionErrorEventArgs(e.ErrorMessage));
        }

        /// <summary>
        /// 屏幕数据接收事件处理
        /// </summary>
        private void OnScreenDataReceived(object? sender, ScreenDataReceivedEventArgs e)
        {
            ScreenDataReceivedDetailed?.Invoke(this, e);

            // 触发简化事件
            if (e.Message != null)
            {
                ScreenDataReceived?.Invoke(this, e.Message);
            }
        }

        /// <summary>
        /// 心跳接收事件处理
        /// </summary>
        private void OnHeartbeatReceived(object? sender, HeartbeatReceivedEventArgs e)
        {
            _logger?.LogDebug("收到心跳消息");
        }

        /// <summary>
        /// 连接响应接收事件处理
        /// </summary>
        private void OnConnectResponseReceived(object? sender, ConnectResponseReceivedEventArgs e)
        {
            _logger?.LogInformation($"连接响应: {e.Success} - {e.Message}");
        }

        /// <summary>
        /// 屏幕录制响应接收事件处理
        /// </summary>
        private void OnScreenCaptureResponseReceived(object? sender, ScreenCaptureResponseReceivedEventArgs e)
        {
            _logger?.LogInformation($"屏幕录制响应: {e.Success} - {e.Message} - 状态: {e.CaptureStatus}");

            // 触发屏幕录制响应事件
            ScreenCaptureResponseReceived?.Invoke(this, e);
        }

        #endregion

        #region 事件触发方法

        protected virtual void OnConnectionStatusChanged(ConnectionStatusChangedEventArgs e)
        {
            ConnectionStatusChanged?.Invoke(this, e);
        }

        protected virtual void OnErrorOccurred(ConnectionErrorEventArgs e)
        {
            ErrorOccurred?.Invoke(this, e);
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            _tcpServer?.Dispose();
        }

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 连接状态变化事件参数
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        public bool IsConnected { get; }
        public string ClientEndPoint { get; }

        public ConnectionStatusChangedEventArgs(bool isConnected, string clientEndPoint)
        {
            IsConnected = isConnected;
            ClientEndPoint = clientEndPoint;
        }
    }

    /// <summary>
    /// 连接错误事件参数
    /// </summary>
    public class ConnectionErrorEventArgs : EventArgs
    {
        public string ErrorMessage { get; }

        public ConnectionErrorEventArgs(string errorMessage)
        {
            ErrorMessage = errorMessage;
        }
    }

    #endregion
}
