using Newtonsoft.Json;

namespace RemoteAndroidControl.WPF.Models.Network
{
    /// <summary>
    /// 屏幕数据消息
    /// 用于传输Android端屏幕截图数据
    /// </summary>
    public class ScreenDataMessage : MessageBase
    {
        public ScreenDataMessage()
        {
            MessageType = MessageTypes.SCREEN_DATA;
        }

        /// <summary>
        /// 屏幕数据
        /// </summary>
        [JsonProperty("data")]
        public new ScreenData? Data { get; set; }
    }

    /// <summary>
    /// 屏幕数据结构
    /// </summary>
    public class ScreenData
    {
        /// <summary>
        /// Base64编码的图像数据
        /// </summary>
        [JsonProperty("imageData")]
        public string ImageData { get; set; } = string.Empty;

        /// <summary>
        /// 屏幕宽度
        /// </summary>
        [JsonProperty("width")]
        public int Width { get; set; }

        /// <summary>
        /// 屏幕高度
        /// </summary>
        [JsonProperty("height")]
        public int Height { get; set; }

        /// <summary>
        /// 图像格式 (JPEG/PNG)
        /// </summary>
        [JsonProperty("format")]
        public string Format { get; set; } = "JPEG";

        /// <summary>
        /// 压缩质量 (0-100)
        /// </summary>
        [JsonProperty("quality")]
        public int Quality { get; set; } = 30;

        /// <summary>
        /// 帧序号
        /// </summary>
        [JsonProperty("frameNumber")]
        public long FrameNumber { get; set; } = 0;

        /// <summary>
        /// 捕获时间
        /// </summary>
        [JsonProperty("captureTime")]
        public long CaptureTime { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

        /// <summary>
        /// 设备信息
        /// </summary>
        [JsonProperty("deviceInfo")]
        public DeviceInfo? DeviceInfo { get; set; }
    }

    /// <summary>
    /// 设备信息结构
    /// </summary>
    public class DeviceInfo
    {
        /// <summary>
        /// 设备型号
        /// </summary>
        [JsonProperty("deviceModel")]
        public string DeviceModel { get; set; } = string.Empty;

        /// <summary>
        /// Android版本
        /// </summary>
        [JsonProperty("androidVersion")]
        public string AndroidVersion { get; set; } = string.Empty;

        /// <summary>
        /// 屏幕密度
        /// </summary>
        [JsonProperty("screenDensity")]
        public int ScreenDensity { get; set; }

        /// <summary>
        /// 屏幕方向
        /// </summary>
        [JsonProperty("orientation")]
        public int Orientation { get; set; }
    }
}
