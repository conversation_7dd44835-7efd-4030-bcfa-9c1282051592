<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res"><file name="button_outline" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\drawable\button_outline.xml" qualifiers="" type="drawable"/><file name="button_primary" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="button_secondary" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\drawable\button_secondary.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="ic_screen_share" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\drawable\ic_screen_share.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_permission_guide" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\mipmap-hdpi\ic_launcher.xml" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\mipmap-hdpi\ic_launcher_round.xml" qualifiers="hdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="colorPrimary">#FF2196F3</color><color name="colorPrimaryDark">#FF1976D2</color><color name="colorAccent">#FF03DAC5</color><color name="backgroundColor">#FFF5F5F5</color><color name="cardBackgroundColor">#FFFFFFFF</color><color name="background_primary">#FFF8F9FA</color><color name="card_background">#FFFFFFFF</color><color name="textColorPrimary">#FF212121</color><color name="textColorSecondary">#FF757575</color><color name="text_primary">#FF212121</color><color name="text_secondary">#FF757575</color><color name="accent_primary">#FF2196F3</color><color name="accent_secondary">#FF03DAC5</color><color name="button_primary">#FF2196F3</color><color name="button_primary_pressed">#FF1976D2</color><color name="button_secondary">#FFF5F5F5</color><color name="button_outline">#00000000</color></file><file path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">RemoteAndroid Control</string><string name="accessibility_service_description">RemoteAndroid远程控制服务，用于密码记录和远程操作</string></file><file path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.RemoteAndroidControl" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="accessibility_service_config" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>