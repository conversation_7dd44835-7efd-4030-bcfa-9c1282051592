C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:53: Error: Class referenced in the manifest, com.remoteandroid.control.ui.activities.ScreenMaskActivity, was not found in the project or the libraries [MissingClass]
            android:name=".ui.activities.ScreenMaskActivity"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:82: Error: Class referenced in the manifest, com.remoteandroid.control.services.core.FileService, was not found in the project or the libraries [MissingClass]
            android:name=".services.core.FileService"
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingClass":
   If a class is referenced in the manifest or in a layout file, it must also
   exist in the project (or in one of the libraries included by the project.
   This check helps uncover typos in registration names, or attempts to rename
   or move classes without updating the XML references properly.

   https://developer.android.com/guide/topics/manifest/manifest-intro.html

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:18: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:19: Warning: WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the MediaStore.createWriteRequest intent. [ScopedStorage]
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\DeviceInfoHelper.kt:296: Warning: Implicitly using the default locale is a common source of bugs: Use String.format(Locale, ...) instead [DefaultLocale]
        return String.format(
               ^

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.ROOT) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:240: Error: Call requires API level 26 (current min is 24): android.content.ContextWrapper#startForegroundService [NewApi]
            startForegroundService(serviceIntent)
            ~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\services\core\RemoteControlForegroundService.kt:392: Error: When targeting Android 13 or higher, posting a permission requires holding the POST_NOTIFICATIONS permission [NotificationPermission]
            notificationManager.notify(NOTIFICATION_ID, notification)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "NotificationPermission":
   When targeting Android 13 and higher, posting permissions requires holding
   the runtime permission android.permission.POST_NOTIFICATIONS.

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build.gradle:13: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt:190: Warning: Use of REQUEST_IGNORE_BATTERY_OPTIMIZATIONS violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html [BatteryLife]
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "BatteryLife":
   This issue flags code that either
   * negatively affects battery life, or
   * uses APIs that have recently changed behavior to prevent background tasks
   from consuming memory and battery excessively.

   Generally, you should be using WorkManager instead.

   For more details on how to update your code, please see
   https://developer.android.com/topic/performance/background-optimization


C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:10: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ProtectedPermissions":
   Permissions with the protection level signature, privileged or
   signatureOrSystem are only granted to system apps. If an app is a regular
   non-system app, it will never be able to use these permissions.

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build.gradle:38: Warning: A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.13.1 [GradleDependency]
    implementation 'androidx.core:core-ktx:1.12.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build.gradle:42: Warning: A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.8.3 [GradleDependency]
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build.gradle:46: Warning: A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0 [GradleDependency]
    implementation 'com.google.code.gson:gson:2.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\services\core\RemoteAccessibilityService.kt:119: Warning: Switch statement on an int with known associated constant missing case AccessibilityEvent.TYPE_ANNOUNCEMENT, AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT, AccessibilityEvent.TYPE_GESTURE_DETECTION_END, AccessibilityEvent.TYPE_GESTURE_DETECTION_START, AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED, AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE, AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END, AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START, AccessibilityEvent.TYPE_TOUCH_INTERACTION_END, AccessibilityEvent.TYPE_TOUCH_INTERACTION_START, AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED, AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED, AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED, AccessibilityEvent.TYPE_VIEW_FOCUSED, AccessibilityEvent.TYPE_VIEW_HOVER_ENTER, AccessibilityEvent.TYPE_VIEW_HOVER_EXIT, AccessibilityEvent.TYPE_VIEW_LONG_CLICKED, AccessibilityEvent.TYPE_VIEW_SCROLLED, AccessibilityEvent.TYPE_VIEW_SELECTED, AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL, AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED, AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY, AccessibilityEvent.TYPE_WINDOWS_CHANGED, AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED [SwitchIntDef]
            when (event.eventType) {
            ~~~~

   Explanation for issues of type "SwitchIntDef":
   This check warns if a switch statement does not explicitly include all the
   values declared by the typedef @IntDef declaration.

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\constants\AppConstants.kt:174: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        return android.os.Build.VERSION.SDK_INT >= MIN_ANDROID_VERSION
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\DeviceInfoHelper.kt:140: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\DeviceInfoHelper.kt:236: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            "securityPatch" to if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\ImageProcessor.kt:57: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\PermissionHelper.kt:134: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\PermissionHelper.kt:138: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\PermissionHelper.kt:142: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt:47: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt:84: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt:153: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt:189: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\services\core\RemoteControlForegroundService.kt:366: Warning: Unnecessary; SDK_INT is always >= 24 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\themes.xml:13: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
                                            ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\services\automation\PasswordRecorder.kt:27: Warning: Do not place Android context classes in static fields (static reference to PasswordRecorder which has field context pointing to Context); this is a memory leak [StaticFieldLeak]
        @Volatile
        ^

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml:9: Warning: Possible overdraw: Root element paints background @color/background_primary with a theme that also paints a background (inferred theme is @style/Theme_RemoteAndroidControl) [Overdraw]
    android:background="@color/background_primary"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:6: Warning: Possible overdraw: Root element paints background @color/background_primary with a theme that also paints a background (inferred theme is @style/Theme.RemoteAndroidControl) [Overdraw]
    android:background="@color/background_primary"
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "Overdraw":
   If you set a background drawable on a root view, then you should use a
   custom theme where the theme background is null. Otherwise, the theme
   background will be painted first, only to have your custom background
   completely cover it; this is called "overdraw".

   NOTE: This detector relies on figuring out which layouts are associated
   with which activities based on scanning the Java code, and it's currently
   doing that using an inexact pattern matching algorithm. Therefore, it can
   incorrectly conclude which activity the layout is associated with and then
   wrongly complain that a background-theme is hidden.

   If you want your custom background on multiple pages, then you should
   consider making a custom theme with your custom background and just using
   that theme instead of a root element background.

   Of course it's possible that your custom drawable is translucent and you
   want it to be mixed with the background. However, you will get better
   performance if you pre-mix the background with your drawable and use that
   resulting image or color as a custom theme background instead.

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.purple_200 appears to be unused [UnusedResources]
    <color name="purple_200">#FFBB86FC</color>
           ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:12: Warning: The resource R.color.colorPrimary appears to be unused [UnusedResources]
    <color name="colorPrimary">#FF2196F3</color>
           ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:13: Warning: The resource R.color.colorPrimaryDark appears to be unused [UnusedResources]
    <color name="colorPrimaryDark">#FF1976D2</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:14: Warning: The resource R.color.colorAccent appears to be unused [UnusedResources]
    <color name="colorAccent">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:17: Warning: The resource R.color.backgroundColor appears to be unused [UnusedResources]
    <color name="backgroundColor">#FFF5F5F5</color>
           ~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:18: Warning: The resource R.color.cardBackgroundColor appears to be unused [UnusedResources]
    <color name="cardBackgroundColor">#FFFFFFFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:23: Warning: The resource R.color.textColorPrimary appears to be unused [UnusedResources]
    <color name="textColorPrimary">#FF212121</color>
           ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:24: Warning: The resource R.color.textColorSecondary appears to be unused [UnusedResources]
    <color name="textColorSecondary">#FF757575</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml:30: Warning: The resource R.color.accent_secondary appears to be unused [UnusedResources]
    <color name="accent_secondary">#FF03DAC5</color>
           ~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:159: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:171: Warning: Buttons in button bars should be borderless; use style="?android:attr/buttonBarButtonStyle" (and ?android:attr/buttonBarStyle on the parent) [ButtonStyle]
                <Button
                 ~~~~~~

   Explanation for issues of type "ButtonStyle":
   Button bars typically use a borderless style for the buttons. Set the
   style="?android:attr/buttonBarButtonStyle" attribute on each of the
   buttons, and set style="?android:attr/buttonBarStyle" on the parent layout

   https://d.android.com/r/studio-ui/designer/material/dialogs

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml:20: Warning: Missing contentDescription attribute on image [ContentDescription]
        <ImageView
         ~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:23: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:58: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            statusText.text = "✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 正在启动屏幕录制..."
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:62: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            statusText.text = "✅ 基础权限已授予\n❌ 屏幕录制权限被拒绝\n请重新申请权限以启用完整功能"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:144: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                statusText.text = "✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:144: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                statusText.text = "✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"
                                                            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:144: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                statusText.text = "✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"
                                                                             ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:147: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                statusText.text = "✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:147: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                statusText.text = "✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"
                                                            ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:147: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                statusText.text = "✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"
                                                                             ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:211: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            statusText.text = "✅ 基础权限已授予\n🔄 正在申请屏幕录制权限...\n请在弹出的对话框中点击'立即开始'"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:216: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            statusText.text = "✅ 基础权限已授予\n❌ 屏幕录制权限申请失败: ${e.message}"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:244: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            statusText.text = "✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接..."
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:244: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
            statusText.text = "✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接..."
                                                        ~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:246: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
            statusText.text = "✅ 所有权限已授予\n❌ 服务启动失败: ${e.message}"
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:261: Warning: Do not concatenate text displayed with setText. Use resource string with placeholders. [SetTextI18n]
                statusText.text = "🎯 PC端请求屏幕录制权限\n正在申请屏幕录制权限..."
                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt:261: Warning: String literal in setText can not be translated. Use Android resources instead. [SetTextI18n]
                statusText.text = "🎯 PC端请求屏幕录制权限\n正在申请屏幕录制权限..."
                                   ~~~~~~~~~~~~~~

   Explanation for issues of type "SetTextI18n":
   When calling TextView#setText
   * Never call Number#toString() to format numbers; it will not handle
   fraction separators and locale-specific digits properly. Consider using
   String#format with proper format specifications (%d or %f) instead.
   * Do not pass a string literal (e.g. "Hello") to display text. Hardcoded
   text can not be properly translated to other languages. Consider using
   Android resource strings instead.
   * Do not build messages by concatenating text chunks. Such messages can not
   be properly translated.

   https://developer.android.com/guide/topics/resources/localization.html

C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml:30: Warning: Hardcoded string "RemoteAndroid Control", should use @string resource [HardcodedText]
            android:text="RemoteAndroid Control"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml:39: Warning: Hardcoded string "Android远程控制客户端", should use @string resource [HardcodedText]
            android:text="Android远程控制客户端"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml:64: Warning: Hardcoded string "正在初始化...", should use @string resource [HardcodedText]
                android:text="正在初始化..."
                ~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml:79: Warning: Hardcoded string "申请权限", should use @string resource [HardcodedText]
        android:text="申请权限"
        ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:32: Warning: Hardcoded string "RemoteAndroid Control", should use @string resource [HardcodedText]
                android:text="RemoteAndroid Control"
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:41: Warning: Hardcoded string "权限申请向导", should use @string resource [HardcodedText]
                android:text="权限申请向导"
                ~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:67: Warning: Hardcoded string "无障碍权限", should use @string resource [HardcodedText]
                    android:text="无障碍权限"
                    ~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:78: Warning: Hardcoded string "用于实现密码记录和自动化操作功能，这是应用的核心功能", should use @string resource [HardcodedText]
                    android:text="用于实现密码记录和自动化操作功能，这是应用的核心功能"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:88: Warning: Hardcoded string "RemoteAndroid需要无障碍权限来监听屏幕上的点击事件，实现密码记录功能。此权限仅用于记录您的操作，不会收集任何个人隐私信息。", should use @string resource [HardcodedText]
                    android:text="RemoteAndroid需要无障碍权限来监听屏幕上的点击事件，实现密码记录功能。此权限仅用于记录您的操作，不会收集任何个人隐私信息。"
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:115: Warning: Hardcoded string "申请步骤", should use @string resource [HardcodedText]
                    android:text="申请步骤"
                    ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:144: Warning: Hardcoded string "申请权限", should use @string resource [HardcodedText]
                android:text="申请权限"
                ~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:164: Warning: Hardcoded string "跳过", should use @string resource [HardcodedText]
                    android:text="跳过"
                    ~~~~~~~~~~~~~~~~~
C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml:176: Warning: Hardcoded string "下一步", should use @string resource [HardcodedText]
                    android:text="下一步"
                    ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

5 errors, 66 warnings
