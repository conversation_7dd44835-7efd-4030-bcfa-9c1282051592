# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.0"
  }
  digests {
    sha256: "$\t8\304\252\270\347>\210\207\003\343\347\323\370s\203\377\345\275Smm^<\020\rL\3207\237\317"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.6.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.6.0"
  }
  digests {
    sha256: "`\261\v^\365v\233yW\001r\340\025\270\025\224\005\311/\003K\250\213\223\221\251wX\234\235\353N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.6.1"
  }
  digests {
    sha256: "~\245W;\223\253\253\323\27521$Q\306\352H\246b\260:\024\r\332\201\256\276uwj \244\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.2"
  }
  digests {
    sha256: "Zg\351\204\361N\322\257\305\205\252:#\355\377\034\027\221\310\f\252+\366\212\017y\234\033\021\243\2208"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.0.1"
  }
  digests {
    sha256: "\217J\230;%\312~\370\312r\315\352\362\3335\247\375\206\372\032\3106\240\r\201\345\0370\264!\r\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.0.1"
  }
  digests {
    sha256: "\037\360sJ%Kw\\\267\347\371\243\001\341\260|\254\032r\275W\037a\204w\363\244\024%q\271O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.0.1"
  }
  digests {
    sha256: "\031C\332\244\243A(a\271\242\275\301\247\310\302\377\005\331\270\031\034\035>V\353\262#\322\353J\205&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.0.1"
  }
  digests {
    sha256: "\004\334\352y\336\n\3704\177E\027\276\302\005x\324$\n\345\356@\031\020 \312\a\327\243\216d\0003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.0.1"
  }
  digests {
    sha256: "\024~\253\277\246\261\022]T\021\2276\321t9\342\301\303E\362\234\355R\252vQ\002\200\226U\276\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.0.1"
  }
  digests {
    sha256: "k\177]\f\224D\203\3529\v=m\250C\216zi\340\317\343\020\275\026\303\305\347\212\300\001[\026c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.0.1"
  }
  digests {
    sha256: "u\261>*\025\332\2121\254\367]\201\304\'\177\362\202\v\033\365\202\221\264h\212\305R\000\315\301\200r"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.0.1"
  }
  digests {
    sha256: "\362v\264\003\373\225\275\242\237\310:3\377qu\221f.\242\307i\3744\302\a~\304`\266_\r\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.6.1"
  }
  digests {
    sha256: "\333\221]\277I5xc\336\026i\377\237\335\216\220\b\326_\343W\257l\316\232\3460C\255_f\027"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.2.0"
  }
  digests {
    sha256: "\363\032\006\301P\354\2600s\365Zo{\vt\242@\246\250\327\'\301L\347g&\320 W\r\372\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.2.0"
  }
  digests {
    sha256: "\177\372MFM\235\262Y\374\240\315\265\017\275J\266=hr\274\332YF\213\237uUPL}Z\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.3.6"
  }
  digests {
    sha256: "\022\360\203\033O\b\t-]\332\',\031#\301\032\002/\362\f\357\376\323\350\001u\036!\273\215\034\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.11.0"
  }
  digests {
    sha256: "\216\257\1779\236\340\224\334\023I\\6\347\324\357G\036\247\357\2129\236My\311kP\375O\202E\f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 8
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 0
}
library_dependencies {
  library_index: 8
  library_dep_index: 6
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 13
  library_dep_index: 14
  library_dep_index: 36
  library_dep_index: 0
  library_dep_index: 5
}
library_dependencies {
  library_index: 9
  library_dep_index: 0
}
library_dependencies {
  library_index: 10
  library_dep_index: 6
}
library_dependencies {
  library_index: 11
  library_dep_index: 6
  library_dep_index: 12
}
library_dependencies {
  library_index: 13
  library_dep_index: 6
}
library_dependencies {
  library_index: 14
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 33
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 34
}
library_dependencies {
  library_index: 15
  library_dep_index: 6
}
library_dependencies {
  library_index: 16
  library_dep_index: 6
  library_dep_index: 15
}
library_dependencies {
  library_index: 17
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 34
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 2
}
library_dependencies {
  library_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 20
  library_dep_index: 1
  library_dep_index: 21
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 21
  library_dep_index: 18
  library_dep_index: 19
  library_dep_index: 20
}
library_dependencies {
  library_index: 22
  library_dep_index: 6
  library_dep_index: 17
  library_dep_index: 17
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 34
}
library_dependencies {
  library_index: 23
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 24
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 19
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 34
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 30
}
library_dependencies {
  library_index: 24
  library_dep_index: 15
  library_dep_index: 16
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 34
}
library_dependencies {
  library_index: 25
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 26
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 24
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 30
  library_dep_index: 23
  library_dep_index: 34
}
library_dependencies {
  library_index: 26
  library_dep_index: 6
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 6
}
library_dependencies {
  library_index: 28
  library_dep_index: 6
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 25
  library_dep_index: 24
  library_dep_index: 30
  library_dep_index: 17
  library_dep_index: 33
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 34
}
library_dependencies {
  library_index: 29
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 30
  library_dep_index: 33
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 34
}
library_dependencies {
  library_index: 30
  library_dep_index: 6
  library_dep_index: 5
  library_dep_index: 24
  library_dep_index: 29
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 31
  library_dep_index: 6
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 31
}
library_dependencies {
  library_index: 33
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 24
  library_dep_index: 0
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 14
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 33
  library_dep_index: 30
}
library_dependencies {
  library_index: 35
  library_dep_index: 6
  library_dep_index: 11
  library_dep_index: 26
  library_dep_index: 12
}
library_dependencies {
  library_index: 36
  library_dep_index: 6
  library_dep_index: 10
}
library_dependencies {
  library_index: 37
  library_dep_index: 38
  library_dep_index: 6
  library_dep_index: 50
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 5
  library_dep_index: 53
  library_dep_index: 54
  library_dep_index: 56
  library_dep_index: 57
  library_dep_index: 58
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 61
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 50
}
library_dependencies {
  library_index: 38
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 35
  library_dep_index: 31
  library_dep_index: 27
  library_dep_index: 0
  library_dep_index: 39
  library_dep_index: 40
}
library_dependencies {
  library_index: 39
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 43
  library_dep_index: 29
  library_dep_index: 0
  library_dep_index: 38
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 38
  library_dep_index: 5
  library_dep_index: 28
  library_dep_index: 33
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 41
  library_dep_index: 18
  library_dep_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 42
  library_dep_index: 41
  library_dep_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 46
  library_dep_index: 48
  library_dep_index: 47
  library_dep_index: 6
  library_dep_index: 4
  library_dep_index: 19
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 49
  library_dep_index: 18
  library_dep_index: 31
  library_dep_index: 22
  library_dep_index: 14
  library_dep_index: 29
  library_dep_index: 35
}
library_dependencies {
  library_index: 44
  library_dep_index: 6
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 0
}
library_dependencies {
  library_index: 45
  library_dep_index: 0
}
library_dependencies {
  library_index: 46
  library_dep_index: 6
  library_dep_index: 47
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 4
}
library_dependencies {
  library_index: 47
  library_dep_index: 44
  library_dep_index: 6
  library_dep_index: 0
  library_dep_index: 41
  library_dep_index: 45
}
library_dependencies {
  library_index: 48
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 6
  library_dep_index: 4
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 10
  library_dep_index: 8
}
library_dependencies {
  library_index: 49
  library_dep_index: 8
}
library_dependencies {
  library_index: 50
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 51
  library_dep_index: 52
  library_dep_index: 37
}
library_dependencies {
  library_index: 51
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 52
  library_dep_index: 51
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 53
  library_dep_index: 6
}
library_dependencies {
  library_index: 54
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 55
}
library_dependencies {
  library_index: 55
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 56
  library_dep_index: 6
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 57
  library_dep_index: 10
  library_dep_index: 8
  library_dep_index: 56
}
library_dependencies {
  library_index: 58
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 38
  library_dep_index: 24
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 9
}
library_dependencies {
  library_index: 59
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 55
}
library_dependencies {
  library_index: 60
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 23
  library_dep_index: 29
}
library_dependencies {
  library_index: 61
  library_dep_index: 6
}
library_dependencies {
  library_index: 62
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 38
  library_dep_index: 6
  library_dep_index: 37
  library_dep_index: 65
  library_dep_index: 66
  library_dep_index: 67
  library_dep_index: 8
  library_dep_index: 54
  library_dep_index: 69
  library_dep_index: 9
  library_dep_index: 58
  library_dep_index: 14
  library_dep_index: 74
  library_dep_index: 61
  library_dep_index: 75
  library_dep_index: 51
  library_dep_index: 76
}
library_dependencies {
  library_index: 63
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 65
  library_dep_index: 6
}
library_dependencies {
  library_index: 66
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 10
}
library_dependencies {
  library_index: 67
  library_dep_index: 37
  library_dep_index: 8
  library_dep_index: 68
}
library_dependencies {
  library_index: 69
  library_dep_index: 8
  library_dep_index: 10
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 71
  library_dep_index: 60
  library_dep_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 71
  library_dep_index: 6
}
library_dependencies {
  library_index: 72
  library_dep_index: 6
}
library_dependencies {
  library_index: 73
  library_dep_index: 6
}
library_dependencies {
  library_index: 74
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 10
}
library_dependencies {
  library_index: 75
  library_dep_index: 6
  library_dep_index: 8
  library_dep_index: 10
}
library_dependencies {
  library_index: 76
  library_dep_index: 6
  library_dep_index: 58
  library_dep_index: 74
  library_dep_index: 8
  library_dep_index: 10
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 37
  dependency_index: 62
  dependency_index: 67
  dependency_index: 28
  dependency_index: 39
  dependency_index: 77
  dependency_index: 18
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
