package com.remoteandroid.control.models.network

import com.google.gson.annotations.SerializedName
import com.remoteandroid.control.constants.NetworkConstants

/**
 * RemoteAndroid Control Suite - 屏幕录制请求消息
 * PC端向Android端请求开始屏幕录制
 */
data class ScreenCaptureRequestMessage(
    @SerializedName("data")
    val requestData: ScreenCaptureRequestData = ScreenCaptureRequestData()
) : MessageBase() {
    
    override val messageType: String = NetworkConstants.MESSAGE_TYPE_REQUEST_SCREEN_CAPTURE
    override val data: Any = requestData
}

/**
 * 屏幕录制请求数据结构
 */
data class ScreenCaptureRequestData(
    @SerializedName("action")
    val action: String = "start", // start/stop
    
    @SerializedName("parameters")
    val parameters: ScreenCaptureParameters = ScreenCaptureParameters()
)

/**
 * 屏幕录制参数
 */
data class ScreenCaptureParameters(
    @SerializedName("width")
    val width: Int = 720,
    
    @SerializedName("height")
    val height: Int = 1280,
    
    @SerializedName("dpi")
    val dpi: Int = 320,
    
    @SerializedName("frameRate")
    val frameRate: Int = 15,
    
    @SerializedName("quality")
    val quality: Int = 30
)
