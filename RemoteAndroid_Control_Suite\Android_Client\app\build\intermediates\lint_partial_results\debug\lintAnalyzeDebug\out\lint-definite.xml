<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.7.0" type="incidents">

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.remoteandroid.control.ui.activities.ScreenMaskActivity`, was not found in the project or the libraries">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="53"
            column="27"
            startOffset="2130"
            endLine="53"
            endColumn="60"
            endOffset="2163"/>
    </incident>

    <incident
        id="MissingClass"
        severity="error"
        message="Class referenced in the manifest, `com.remoteandroid.control.services.core.FileService`, was not found in the project or the libraries">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="82"
            column="27"
            startOffset="3179"
            endLine="82"
            endColumn="53"
            endOffset="3205"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/utils/DeviceInfoHelper.kt"
            line="296"
            column="16"
            startOffset="9515"
            endLine="300"
            endColumn="10"
            endOffset="9666"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="34"
            replacement="36"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="13"
            column="9"
            startOffset="251"
            endLine="13"
            endColumn="21"
            endOffset="263"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="10"
            column="22"
            startOffset="372"
            endLine="10"
            endColumn="82"
            endOffset="432"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.13.1">
        <fix-replace
            description="Change to 1.13.1"
            family="Update versions"
            oldString="1.12.0"
            replacement="1.13.1"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="38"
            column="20"
            startOffset="813"
            endLine="38"
            endColumn="51"
            endOffset="844"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.8.3">
        <fix-replace
            description="Change to 2.8.3"
            family="Update versions"
            oldString="2.7.0"
            replacement="2.8.3"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="42"
            column="20"
            startOffset="1055"
            endLine="42"
            endColumn="68"
            endOffset="1103"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0">
        <fix-replace
            description="Change to 2.11.0"
            family="Update versions"
            oldString="2.10.1"
            replacement="2.11.0"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="46"
            column="20"
            startOffset="1204"
            endLine="46"
            endColumn="54"
            endOffset="1238"/>
    </incident>

    <incident
        id="SwitchIntDef"
        severity="warning"
        message="Switch statement on an `int` with known associated constant missing case `AccessibilityEvent.TYPE_ANNOUNCEMENT`, `AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_END`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_START`, `AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED`, `AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_END`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_START`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED`, `AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED`, `AccessibilityEvent.TYPE_VIEW_FOCUSED`, `AccessibilityEvent.TYPE_VIEW_HOVER_ENTER`, `AccessibilityEvent.TYPE_VIEW_HOVER_EXIT`, `AccessibilityEvent.TYPE_VIEW_LONG_CLICKED`, `AccessibilityEvent.TYPE_VIEW_SCROLLED`, `AccessibilityEvent.TYPE_VIEW_SELECTED`, `AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL`, `AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED`, `AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY`, `AccessibilityEvent.TYPE_WINDOWS_CHANGED`, `AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED`">
        <fix-data cases="android.view.accessibility.AccessibilityEvent.TYPE_ANNOUNCEMENT, android.view.accessibility.AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT, android.view.accessibility.AccessibilityEvent.TYPE_GESTURE_DETECTION_END, android.view.accessibility.AccessibilityEvent.TYPE_GESTURE_DETECTION_START, android.view.accessibility.AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_INTERACTION_END, android.view.accessibility.AccessibilityEvent.TYPE_TOUCH_INTERACTION_START, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_FOCUSED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_HOVER_ENTER, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_HOVER_EXIT, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_LONG_CLICKED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_SCROLLED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_SELECTED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY, android.view.accessibility.AccessibilityEvent.TYPE_WINDOWS_CHANGED, android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/services/core/RemoteAccessibilityService.kt"
            line="119"
            column="13"
            startOffset="3959"
            endLine="119"
            endColumn="17"
            endOffset="3963"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/constants/AppConstants.kt"
            line="174"
            column="16"
            startOffset="6041"
            endLine="174"
            endColumn="71"
            endOffset="6096"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/utils/DeviceInfoHelper.kt"
            line="140"
            column="17"
            startOffset="4430"
            endLine="140"
            endColumn="70"
            endOffset="4483"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/utils/DeviceInfoHelper.kt"
            line="236"
            column="36"
            startOffset="7332"
            endLine="236"
            endColumn="82"
            endOffset="7378"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/utils/ImageProcessor.kt"
            line="57"
            column="13"
            startOffset="1559"
            endLine="57"
            endColumn="98"
            endOffset="1644"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/utils/PermissionHelper.kt"
            line="134"
            column="17"
            startOffset="4227"
            endLine="134"
            endColumn="92"
            endOffset="4302"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/utils/PermissionHelper.kt"
            line="138"
            column="17"
            startOffset="4394"
            endLine="138"
            endColumn="85"
            endOffset="4462"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/utils/PermissionHelper.kt"
            line="142"
            column="17"
            startOffset="4566"
            endLine="142"
            endColumn="85"
            endOffset="4634"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/managers/PermissionManager.kt"
            line="47"
            column="20"
            startOffset="1319"
            endLine="47"
            endColumn="66"
            endOffset="1365"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/managers/PermissionManager.kt"
            line="84"
            column="20"
            startOffset="2531"
            endLine="84"
            endColumn="66"
            endOffset="2577"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/managers/PermissionManager.kt"
            line="153"
            column="13"
            startOffset="5034"
            endLine="153"
            endColumn="59"
            endOffset="5080"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/managers/PermissionManager.kt"
            line="189"
            column="13"
            startOffset="6156"
            endLine="189"
            endColumn="59"
            endOffset="6202"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/services/core/RemoteControlForegroundService.kt"
            line="366"
            column="17"
            startOffset="11889"
            endLine="366"
            endColumn="63"
            endOffset="11935"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `PasswordRecorder` which has field `context` pointing to `Context`); this is a memory leak">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/services/automation/PasswordRecorder.kt"
            line="27"
            column="9"
            startOffset="837"
            endLine="28"
            endColumn="55"
            endOffset="901"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme_RemoteAndroidControl`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="9"
            column="5"
            startOffset="366"
            endLine="9"
            endColumn="51"
            endOffset="412"/>
    </incident>

    <incident
        id="Overdraw"
        severity="warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.RemoteAndroidControl`)">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="6"
            column="5"
            startOffset="251"
            endLine="6"
            endColumn="51"
            endOffset="297"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
                    startOffset="5809"
                    endOffset="7069"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
                    startOffset="6066"
                    endOffset="6528"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
                    startOffset="6577"
                    endOffset="7040"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="159"
            column="18"
            startOffset="6067"
            endLine="159"
            endColumn="24"
            endOffset="6073"/>
    </incident>

    <incident
        id="ButtonStyle"
        severity="warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)">
        <fix-composite
            description="Make borderless">
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
                    startOffset="5809"
                    endOffset="7069"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
                    startOffset="6066"
                    endOffset="6528"/>
            </fix-attribute>
            <fix-attribute
                description="Set style=&quot;?android:attr/buttonBarButtonStyle&quot;"
                attribute="style"
                value="?android:attr/buttonBarButtonStyle">
                <range
                    file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
                    startOffset="6577"
                    endOffset="7040"/>
            </fix-attribute>
        </fix-composite>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="171"
            column="18"
            startOffset="6578"
            endLine="171"
            endColumn="24"
            endOffset="6584"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="20"
            column="10"
            startOffset="716"
            endLine="20"
            endColumn="19"
            endOffset="725"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="23"
            column="14"
            startOffset="796"
            endLine="23"
            endColumn="23"
            endOffset="805"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="58"
            column="31"
            startOffset="2092"
            endLine="58"
            endColumn="72"
            endOffset="2133"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="62"
            column="31"
            startOffset="2264"
            endLine="62"
            endColumn="71"
            endOffset="2304"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="144"
            column="35"
            startOffset="4650"
            endLine="144"
            endColumn="102"
            endOffset="4717"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="144"
            column="61"
            startOffset="4676"
            endLine="144"
            endColumn="74"
            endOffset="4689"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="144"
            column="78"
            startOffset="4693"
            endLine="144"
            endColumn="101"
            endOffset="4716"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="147"
            column="35"
            startOffset="4826"
            endLine="147"
            endColumn="102"
            endOffset="4893"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="147"
            column="61"
            startOffset="4852"
            endLine="147"
            endColumn="74"
            endOffset="4865"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="147"
            column="78"
            startOffset="4869"
            endLine="147"
            endColumn="101"
            endOffset="4892"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="211"
            column="31"
            startOffset="7220"
            endLine="211"
            endColumn="79"
            endOffset="7268"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="216"
            column="31"
            startOffset="7437"
            endLine="216"
            endColumn="70"
            endOffset="7476"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="244"
            column="31"
            startOffset="8408"
            endLine="244"
            endColumn="71"
            endOffset="8448"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="244"
            column="57"
            startOffset="8434"
            endLine="244"
            endColumn="70"
            endOffset="8447"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="246"
            column="31"
            startOffset="8512"
            endLine="246"
            endColumn="66"
            endOffset="8547"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="261"
            column="35"
            startOffset="8984"
            endLine="261"
            endColumn="66"
            endOffset="9015"/>
    </incident>

    <incident
        id="SetTextI18n"
        severity="warning"
        message="String literal in `setText` can not be translated. Use Android resources instead.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="261"
            column="36"
            startOffset="8985"
            endLine="261"
            endColumn="50"
            endOffset="8999"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;RemoteAndroid Control&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="30"
            column="13"
            startOffset="1070"
            endLine="30"
            endColumn="49"
            endOffset="1106"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Android远程控制客户端&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="39"
            column="13"
            startOffset="1409"
            endLine="39"
            endColumn="42"
            endOffset="1438"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;正在初始化...&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="64"
            column="17"
            startOffset="2238"
            endLine="64"
            endColumn="40"
            endOffset="2261"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;申请权限&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="79"
            column="9"
            startOffset="2681"
            endLine="79"
            endColumn="28"
            endOffset="2700"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;RemoteAndroid Control&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="32"
            column="17"
            startOffset="1143"
            endLine="32"
            endColumn="53"
            endOffset="1179"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;权限申请向导&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="41"
            column="17"
            startOffset="1514"
            endLine="41"
            endColumn="38"
            endOffset="1535"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;无障碍权限&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="67"
            column="21"
            startOffset="2479"
            endLine="67"
            endColumn="41"
            endOffset="2499"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;用于实现密码记录和自动化操作功能，这是应用的核心功能&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="78"
            column="21"
            startOffset="2962"
            endLine="78"
            endColumn="62"
            endOffset="3003"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;RemoteAndroid需要无障碍权限来监听屏幕上的点击事件，实现密码记录功能。此权限仅用于记录您的操作，不会收集任何个人隐私信息。&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="88"
            column="21"
            startOffset="3415"
            endLine="88"
            endColumn="103"
            endOffset="3497"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;申请步骤&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="115"
            column="21"
            startOffset="4462"
            endLine="115"
            endColumn="40"
            endOffset="4481"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;申请权限&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="144"
            column="17"
            startOffset="5502"
            endLine="144"
            endColumn="36"
            endOffset="5521"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;跳过&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="164"
            column="21"
            startOffset="6283"
            endLine="164"
            endColumn="38"
            endOffset="6300"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;下一步&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_permission_guide.xml"
            line="176"
            column="21"
            startOffset="6794"
            endLine="176"
            endColumn="39"
            endOffset="6812"/>
    </incident>

</incidents>
