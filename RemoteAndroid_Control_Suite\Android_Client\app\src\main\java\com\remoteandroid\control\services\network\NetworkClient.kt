package com.remoteandroid.control.services.network

import android.util.Log
import com.google.gson.Gson
import com.remoteandroid.control.constants.NetworkConstants
import com.remoteandroid.control.models.network.MessageBase
import kotlinx.coroutines.*
import java.io.*
import java.net.Socket
import java.net.SocketException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * RemoteAndroid Control Suite - 网络客户端
 * 负责与PC端建立TCP连接并进行消息通信
 */
class NetworkClient private constructor() {
    
    companion object {
        private const val TAG = "NetworkClient"
        
        @Volatile
        private var INSTANCE: NetworkClient? = null
        
        fun getInstance(): NetworkClient {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkClient().also { INSTANCE = it }
            }
        }
    }
    
    private var socket: Socket? = null
    private var outputStream: PrintWriter? = null
    private var inputStream: BufferedReader? = null
    private var gson = Gson()
    
    private val isConnected = AtomicBoolean(false)
    private val isConnecting = AtomicBoolean(false)
    
    private var networkScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var heartbeatJob: Job? = null
    private var receiveJob: Job? = null
    
    // 连接参数 (硬编码配置)
    private var serverHost = NetworkConstants.DEFAULT_SERVER_HOST
    private var serverPort = NetworkConstants.DEFAULT_SERVER_PORT
    
    // 事件监听器
    private var connectionListener: ConnectionListener? = null
    private var messageListener: MessageListener? = null
    
    /**
     * 设置服务器地址
     */
    fun setServerAddress(host: String, port: Int) {
        if (!isConnected.get()) {
            serverHost = host
            serverPort = port
            Log.d(TAG, "Server address set to $host:$port")
        } else {
            Log.w(TAG, "Cannot change server address while connected")
        }
    }
    
    /**
     * 设置连接监听器
     */
    fun setConnectionListener(listener: ConnectionListener) {
        connectionListener = listener
    }
    
    /**
     * 设置消息监听器
     */
    fun setMessageListener(listener: MessageListener) {
        messageListener = listener
    }
    
    /**
     * 连接到服务器
     */
    fun connect() {
        if (isConnected.get() || isConnecting.get()) {
            Log.w(TAG, "Already connected or connecting")
            return
        }
        
        isConnecting.set(true)
        
        networkScope.launch {
            try {
                Log.d(TAG, "Connecting to $serverHost:$serverPort")
                
                socket = Socket(serverHost, serverPort)
                outputStream = PrintWriter(socket!!.getOutputStream(), true)
                inputStream = BufferedReader(InputStreamReader(socket!!.getInputStream()))
                
                isConnected.set(true)
                isConnecting.set(false)
                
                Log.i(TAG, "Connected to server successfully")
                
                // 通知连接成功
                withContext(Dispatchers.Main) {
                    connectionListener?.onConnected()
                }
                
                // 开始心跳和消息接收
                startHeartbeat()
                startReceiving()
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to connect to server", e)
                isConnecting.set(false)
                
                withContext(Dispatchers.Main) {
                    connectionListener?.onConnectionFailed(e.message ?: "Unknown error")
                }
                
                cleanup()
            }
        }
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        if (!isConnected.get()) {
            return
        }
        
        Log.d(TAG, "Disconnecting from server")
        
        isConnected.set(false)
        
        // 停止心跳和接收
        heartbeatJob?.cancel()
        receiveJob?.cancel()
        
        cleanup()
        
        connectionListener?.onDisconnected()
    }
    
    /**
     * 发送消息
     */
    fun sendMessage(message: MessageBase) {
        if (!isConnected.get()) {
            Log.w(TAG, "Not connected, cannot send message")
            return
        }
        
        networkScope.launch {
            try {
                val jsonMessage = gson.toJson(message)
                outputStream?.println(jsonMessage)
                
                Log.d(TAG, "Message sent: ${message.messageType}")
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to send message", e)
                
                if (e is SocketException) {
                    // 连接断开，触发重连
                    handleConnectionLost()
                }
            }
        }
    }
    
    /**
     * 开始心跳检测
     */
    private fun startHeartbeat() {
        heartbeatJob = networkScope.launch {
            while (isConnected.get()) {
                try {
                    // 发送心跳消息
                    val heartbeatMessage = createHeartbeatMessage()
                    sendMessage(heartbeatMessage)
                    
                    delay(NetworkConstants.HEARTBEAT_INTERVAL)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Heartbeat failed", e)
                    handleConnectionLost()
                    break
                }
            }
        }
    }
    
    /**
     * 开始接收消息
     */
    private fun startReceiving() {
        receiveJob = networkScope.launch {
            try {
                while (isConnected.get()) {
                    val line = inputStream?.readLine()
                    if (line != null) {
                        processReceivedMessage(line)
                    } else {
                        // 连接断开
                        Log.w(TAG, "Received null message, connection lost")
                        handleConnectionLost()
                        break
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error receiving messages", e)
                handleConnectionLost()
            }
        }
    }
    
    /**
     * 处理接收到的消息
     */
    private suspend fun processReceivedMessage(jsonMessage: String) {
        try {
            Log.d(TAG, "Processing received message: $jsonMessage")

            // 先解析JSON获取消息类型
            val jsonObject = gson.fromJson(jsonMessage, com.google.gson.JsonObject::class.java)
            val messageType = jsonObject.get("messageType")?.asString

            Log.d(TAG, "Message type: $messageType")

            // 根据消息类型创建具体的消息对象
            val message = when (messageType) {
                NetworkConstants.MESSAGE_TYPE_REQUEST_SCREEN_CAPTURE -> {
                    // 创建屏幕录制请求消息
                    object : MessageBase() {
                        override val messageType: String = NetworkConstants.MESSAGE_TYPE_REQUEST_SCREEN_CAPTURE
                        override val data: Any? = jsonObject.get("data")
                    }
                }
                NetworkConstants.MESSAGE_TYPE_HEARTBEAT -> {
                    // 创建心跳消息
                    object : MessageBase() {
                        override val messageType: String = NetworkConstants.MESSAGE_TYPE_HEARTBEAT
                        override val data: Any? = jsonObject.get("data")
                    }
                }
                NetworkConstants.MESSAGE_TYPE_CLICK -> {
                    // 创建点击消息
                    object : MessageBase() {
                        override val messageType: String = NetworkConstants.MESSAGE_TYPE_CLICK
                        override val data: Any? = jsonObject.get("data")
                    }
                }
                else -> {
                    // 对于未知消息类型，记录详细信息
                    Log.w(TAG, "Unknown message type received: $messageType")
                    Log.w(TAG, "Full message: $jsonMessage")
                    object : MessageBase() {
                        override val messageType: String = messageType ?: "UNKNOWN"
                        override val data: Any? = jsonObject.get("data")
                    }
                }
            }

            Log.d(TAG, "Message processed successfully: ${message.messageType}")

            withContext(Dispatchers.Main) {
                messageListener?.onMessageReceived(message)
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to process received message: $jsonMessage", e)
        }
    }
    
    /**
     * 处理连接丢失
     */
    private suspend fun handleConnectionLost() {
        if (!isConnected.get()) {
            return
        }
        
        Log.w(TAG, "Connection lost, attempting to reconnect")
        
        isConnected.set(false)
        cleanup()
        
        withContext(Dispatchers.Main) {
            connectionListener?.onConnectionLost()
        }
        
        // 自动重连
        delay(NetworkConstants.RECONNECT_DELAY)
        if (!isConnected.get()) {
            connect()
        }
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            outputStream?.close()
            inputStream?.close()
            socket?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up resources", e)
        }
        
        outputStream = null
        inputStream = null
        socket = null
    }
    
    /**
     * 创建心跳消息
     */
    private fun createHeartbeatMessage(): MessageBase {
        return object : MessageBase() {
            override val messageType = NetworkConstants.MESSAGE_TYPE_HEARTBEAT
            override val data: Any? = null
        }
    }
    
    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean = isConnected.get()
    
    /**
     * 获取连接信息
     */
    fun getConnectionInfo(): String {
        return if (isConnected.get()) {
            "Connected to $serverHost:$serverPort"
        } else {
            "Not connected"
        }
    }
    
    /**
     * 销毁网络客户端
     */
    fun destroy() {
        disconnect()
        networkScope.cancel()
    }
    
    /**
     * 连接状态监听器
     */
    interface ConnectionListener {
        fun onConnected()
        fun onDisconnected()
        fun onConnectionLost()
        fun onConnectionFailed(error: String)
    }
    
    /**
     * 发送心跳消息
     */
    fun sendHeartbeat(status: Map<String, Any>) {
        // 创建心跳消息并发送
        // 这里可以创建一个HeartbeatMessage类，暂时使用日志
        Log.d(TAG, "Sending heartbeat with status: $status")
    }

    /**
     * 消息监听器
     */
    interface MessageListener {
        fun onMessageReceived(message: MessageBase)
    }
}
