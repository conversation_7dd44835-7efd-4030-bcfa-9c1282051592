[{"merged": "com.remoteandroid.control.app-debug-45:/layout_activity_main.xml.flat", "source": "com.remoteandroid.control.app-main-47:/layout/activity_main.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/drawable_button_secondary.xml.flat", "source": "com.remoteandroid.control.app-main-47:/drawable/button_secondary.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/drawable_button_primary.xml.flat", "source": "com.remoteandroid.control.app-main-47:/drawable/button_primary.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/drawable_ic_screen_share.xml.flat", "source": "com.remoteandroid.control.app-main-47:/drawable/ic_screen_share.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/mipmap-hdpi_ic_launcher_round.xml.flat", "source": "com.remoteandroid.control.app-main-47:/mipmap-hdpi/ic_launcher_round.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/layout_activity_permission_guide.xml.flat", "source": "com.remoteandroid.control.app-main-47:/layout/activity_permission_guide.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/drawable_button_outline.xml.flat", "source": "com.remoteandroid.control.app-main-47:/drawable/button_outline.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/xml_backup_rules.xml.flat", "source": "com.remoteandroid.control.app-main-47:/xml/backup_rules.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/xml_data_extraction_rules.xml.flat", "source": "com.remoteandroid.control.app-main-47:/xml/data_extraction_rules.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/drawable_ic_notification.xml.flat", "source": "com.remoteandroid.control.app-main-47:/drawable/ic_notification.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/xml_accessibility_service_config.xml.flat", "source": "com.remoteandroid.control.app-main-47:/xml/accessibility_service_config.xml"}, {"merged": "com.remoteandroid.control.app-debug-45:/mipmap-hdpi_ic_launcher.xml.flat", "source": "com.remoteandroid.control.app-main-47:/mipmap-hdpi/ic_launcher.xml"}]