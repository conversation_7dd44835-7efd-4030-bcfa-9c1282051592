{"logs": [{"outputFile": "com.remoteandroid.control.app-mergeReleaseResources-43:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\a1370fee3db5402869b59d38e9f7680e\\transformed\\appcompat-1.6.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,536,648,736,839,954,1033,1110,1201,1294,1389,1483,1583,1676,1771,1865,1956,2049,2130,2234,2337,2435,2542,2649,2754,2911,10156", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "424,531,643,731,834,949,1028,1105,1196,1289,1384,1478,1578,1671,1766,1860,1951,2044,2125,2229,2332,2430,2537,2644,2749,2906,3002,10233"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7d5087baff1a4001a5b77a578bce78ed\\transformed\\material-1.11.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1071,1174,1249,1312,1404,1475,1540,1607,1679,1751,1805,1926,1985,2049,2103,2180,2312,2397,2478,2627,2714,2797,2939,3031,3109,3165,3223,3289,3361,3438,3529,3612,3692,3771,3846,3925,4029,4119,4192,4286,4383,4457,4530,4629,4684,4768,4836,4924,5013,5075,5139,5202,5273,5382,5493,5596,5704,5764,5826", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "266,344,421,498,592,680,792,918,999,1066,1169,1244,1307,1399,1470,1535,1602,1674,1746,1800,1921,1980,2044,2098,2175,2307,2392,2473,2622,2709,2792,2934,3026,3104,3160,3218,3284,3356,3433,3524,3607,3687,3766,3841,3920,4024,4114,4187,4281,4378,4452,4525,4624,4679,4763,4831,4919,5008,5070,5134,5197,5268,5377,5488,5591,5699,5759,5821,5903"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,51,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3085,3162,3239,3333,4151,4263,4389,4752,4819,4922,5185,5248,5340,5411,5476,5543,5615,5687,5741,5862,5921,5985,6039,6116,6248,6333,6414,6563,6650,6733,6875,6967,7045,7101,7159,7225,7297,7374,7465,7548,7628,7707,7782,7861,7965,8055,8128,8222,8319,8393,8466,8565,8620,8704,8772,8860,8949,9011,9075,9138,9209,9318,9429,9532,9640,9700,10074", "endLines": "5,33,34,35,36,37,45,46,47,51,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,116", "endColumns": "12,77,76,76,93,87,111,125,80,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,80,148,86,82,141,91,77,55,57,65,71,76,90,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81", "endOffsets": "316,3080,3157,3234,3328,3416,4258,4384,4465,4814,4917,4992,5243,5335,5406,5471,5538,5610,5682,5736,5857,5916,5980,6034,6111,6243,6328,6409,6558,6645,6728,6870,6962,7040,7096,7154,7220,7292,7369,7460,7543,7623,7702,7777,7856,7960,8050,8123,8217,8314,8388,8461,8560,8615,8699,8767,8855,8944,9006,9070,9133,9204,9313,9424,9527,9635,9695,9757,10151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\46addebd337d7ba2893b2181119dd40d\\transformed\\core-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3421,3519,3623,3722,3825,3931,4038,10314", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "3514,3618,3717,3820,3926,4033,4146,10410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\2d35c5093cb170b93636a303cf46f35a\\transformed\\ui-1.0.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,282,387,469,575,668,751,819,887,963,1033", "endColumns": "94,81,104,81,105,92,82,67,67,75,69,123", "endOffsets": "195,277,382,464,570,663,746,814,882,958,1028,1152"}, "to": {"startLines": "48,49,50,54,55,112,113,114,115,118,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4470,4565,4647,4997,5079,9762,9855,9938,10006,10238,10415,10485", "endColumns": "94,81,104,81,105,92,82,67,67,75,69,123", "endOffsets": "4560,4642,4747,5074,5180,9850,9933,10001,10069,10309,10480,10604"}}]}]}