﻿#pragma checksum "..\..\..\..\..\Views\Modules\ScreenControlView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "159FAC3C899AF3B8AA55E7FC90AC12B1AFFACB1A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using RemoteAndroidControl.WPF.Views.Modules;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RemoteAndroidControl.WPF.Views.Modules {
    
    
    /// <summary>
    /// ScreenControlView
    /// </summary>
    public partial class ScreenControlView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceInfoText;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartCaptureButton;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopCaptureButton;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Viewbox ScreenViewbox;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ScreenBorder;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image ScreenImage;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NoConnectionPanel;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse ConnectionStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FpsText;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DelayText;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResolutionText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RemoteAndroidControl.WPF;component/views/modules/screencontrolview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DeviceInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.StartCaptureButton = ((System.Windows.Controls.Button)(target));
            
            #line 38 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
            this.StartCaptureButton.Click += new System.Windows.RoutedEventHandler(this.StartCaptureButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.StopCaptureButton = ((System.Windows.Controls.Button)(target));
            
            #line 41 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
            this.StopCaptureButton.Click += new System.Windows.RoutedEventHandler(this.StopCaptureButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 44 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ScreenViewbox = ((System.Windows.Controls.Viewbox)(target));
            return;
            case 6:
            this.ScreenBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.ScreenImage = ((System.Windows.Controls.Image)(target));
            
            #line 57 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
            this.ScreenImage.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ScreenImage_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 58 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
            this.ScreenImage.MouseRightButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ScreenImage_MouseRightButtonDown);
            
            #line default
            #line hidden
            
            #line 59 "..\..\..\..\..\Views\Modules\ScreenControlView.xaml"
            this.ScreenImage.MouseMove += new System.Windows.Input.MouseEventHandler(this.ScreenImage_MouseMove);
            
            #line default
            #line hidden
            return;
            case 8:
            this.NoConnectionPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.LoadingPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.ConnectionStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 11:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.FpsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.DelayText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ResolutionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

