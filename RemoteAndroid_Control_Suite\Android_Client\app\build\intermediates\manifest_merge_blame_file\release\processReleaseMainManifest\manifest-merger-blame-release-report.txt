1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.remoteandroid.control"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 无障碍服务权限 -->
16    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
16-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:10:5-85
16-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:10:22-82
17
18    <!-- 屏幕录制权限 -->
19    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
19-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:13:5-77
19-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:13:22-74
20    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
20-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:14:5-94
20-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:14:22-91
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:15:5-78
21-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:15:22-75
22
23    <!-- 文件访问权限 -->
24    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
24-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:18:5-80
24-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:18:22-77
25    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
25-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:19:5-81
25-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:19:22-78
26
27    <!-- 电池优化权限 -->
28    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
28-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:22:5-95
28-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:22:22-92
29
30    <permission
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
31        android:name="com.remoteandroid.control.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
32        android:protectionLevel="signature" />
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
33
34    <uses-permission android:name="com.remoteandroid.control.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
35
36    <application
36-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:24:5-85:19
37        android:allowBackup="true"
37-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:25:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\46addebd337d7ba2893b2181119dd40d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
39        android:dataExtractionRules="@xml/data_extraction_rules"
39-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:26:9-65
40        android:extractNativeLibs="false"
41        android:fullBackupContent="@xml/backup_rules"
41-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:27:9-54
42        android:icon="@mipmap/ic_launcher"
42-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:28:9-43
43        android:label="@string/app_name"
43-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:29:9-41
44        android:roundIcon="@mipmap/ic_launcher_round"
44-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:30:9-54
45        android:supportsRtl="true"
45-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:31:9-35
46        android:theme="@style/Theme.RemoteAndroidControl" >
46-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:32:9-58
47
48        <!-- 主Activity -->
49        <activity
49-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:36:9-44:20
50            android:name="com.remoteandroid.control.ui.activities.MainActivity"
50-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:37:13-55
51            android:exported="true"
51-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:38:13-36
52            android:theme="@style/Theme.RemoteAndroidControl" >
52-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:39:13-62
53            <intent-filter>
53-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:40:13-43:29
54                <action android:name="android.intent.action.MAIN" />
54-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:41:17-69
54-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:41:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:42:17-77
56-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:42:27-74
57            </intent-filter>
58        </activity>
59
60        <!-- 权限引导Activity -->
61        <activity
61-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:47:9-49:40
62            android:name="com.remoteandroid.control.ui.activities.PermissionGuideActivity"
62-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:48:13-66
63            android:exported="false" />
63-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:49:13-37
64
65        <!-- 屏幕遮蔽Activity -->
66        <activity
66-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:52:9-54:40
67            android:name="com.remoteandroid.control.ui.activities.ScreenMaskActivity"
67-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:53:13-61
68            android:exported="false" />
68-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:54:13-37
69
70        <!-- 无障碍服务 -->
71        <service
71-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:57:9-67:19
72            android:name="com.remoteandroid.control.services.core.RemoteAccessibilityService"
72-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:58:13-69
73            android:exported="false"
73-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:59:13-37
74            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
74-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:60:13-79
75            <intent-filter>
75-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:61:13-63:29
76                <action android:name="android.accessibilityservice.AccessibilityService" />
76-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:62:17-92
76-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:62:25-89
77            </intent-filter>
78
79            <meta-data
79-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:64:13-66:72
80                android:name="android.accessibilityservice"
80-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:65:17-60
81                android:resource="@xml/accessibility_service_config" />
81-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:66:17-69
82        </service>
83
84        <!-- 前台服务 -->
85        <service
85-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:70:9-73:63
86            android:name="com.remoteandroid.control.services.core.RemoteControlForegroundService"
86-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:71:13-73
87            android:exported="false"
87-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:72:13-37
88            android:foregroundServiceType="mediaProjection" />
88-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:73:13-60
89
90        <!-- 屏幕录制服务 -->
91        <service
91-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:76:9-78:40
92            android:name="com.remoteandroid.control.services.core.ScreenCaptureService"
92-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:77:13-63
93            android:exported="false" />
93-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:78:13-37
94
95        <!-- 文件服务 -->
96        <service
96-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:81:9-83:40
97            android:name="com.remoteandroid.control.services.core.FileService"
97-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:82:13-54
98            android:exported="false" />
98-->C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml:83:13-37
99
100        <provider
100-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
101            android:name="androidx.startup.InitializationProvider"
101-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
102            android:authorities="com.remoteandroid.control.androidx-startup"
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
103            android:exported="false" >
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
104            <meta-data
104-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.emoji2.text.EmojiCompatInitializer"
105-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
106                android:value="androidx.startup" />
106-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\060b9a6d6318e6905656ad3e8fc54a37\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
108-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
109                android:value="androidx.startup" />
109-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\66b232406350481cd6fe7f04a71cfe84\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
112                android:value="androidx.startup" />
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
113        </provider>
114
115        <receiver
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
116            android:name="androidx.profileinstaller.ProfileInstallReceiver"
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
117            android:directBootAware="false"
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
118            android:enabled="true"
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
119            android:exported="true"
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
120            android:permission="android.permission.DUMP" >
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
122                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
125                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
128                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
131                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.14.3\transforms\d5a60062b780eb83fe2dc68cc888e5eb\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
132            </intent-filter>
133        </receiver>
134    </application>
135
136</manifest>
