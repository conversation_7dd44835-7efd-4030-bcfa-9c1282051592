<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- 顶部标题区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="32dp">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@mipmap/ic_launcher"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="RemoteAndroid Control"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="权限申请向导"
                android:textSize="16sp"
                android:textColor="@color/text_secondary" />

        </LinearLayout>

        <!-- 权限信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- 权限标题 -->
                <TextView
                    android:id="@+id/tv_permission_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="无障碍权限"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/accent_primary"
                    android:layout_marginBottom="8dp" />

                <!-- 权限描述 -->
                <TextView
                    android:id="@+id/tv_permission_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="用于实现密码记录和自动化操作功能，这是应用的核心功能"
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- 权限说明 -->
                <TextView
                    android:id="@+id/tv_permission_reason"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="RemoteAndroid需要无障碍权限来监听屏幕上的点击事件，实现密码记录功能。此权限仅用于记录您的操作，不会收集任何个人隐私信息。"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 申请步骤卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="申请步骤"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="16dp" />

                <!-- 步骤列表容器 -->
                <LinearLayout
                    android:id="@+id/ll_steps_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 底部按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="24dp">

            <!-- 申请权限按钮 -->
            <Button
                android:id="@+id/btn_request_permission"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="申请权限"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/white"
                android:background="@drawable/button_primary"
                android:layout_marginBottom="12dp" />

            <!-- 底部操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- 跳过按钮 -->
                <Button
                    android:id="@+id/btn_skip"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="跳过"
                    android:textSize="14sp"
                    android:textColor="@color/text_secondary"
                    android:background="@drawable/button_secondary"
                    android:layout_marginEnd="8dp" />

                <!-- 下一步按钮 -->
                <Button
                    android:id="@+id/btn_next"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="下一步"
                    android:textSize="14sp"
                    android:textColor="@color/accent_primary"
                    android:background="@drawable/button_outline"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
