<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.7.0">

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.remoteandroid.control.ui.activities.ScreenMaskActivity`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;.ui.activities.ScreenMaskActivity&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml"
            line="53"
            column="27"/>
    </issue>

    <issue
        id="MissingClass"
        severity="Error"
        message="Class referenced in the manifest, `com.remoteandroid.control.services.core.FileService`, was not found in the project or the libraries"
        category="Correctness"
        priority="8"
        summary="Missing registered class"
        explanation="If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly."
        url="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        urls="https://developer.android.com/guide/topics/manifest/manifest-intro.html"
        errorLine1="            android:name=&quot;.services.core.FileService&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml"
            line="82"
            column="27"/>
    </issue>

    <issue
        id="ScopedStorage"
        severity="Warning"
        message="READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: `READ_MEDIA_IMAGES`, `READ_MEDIA_VIDEO` or `READ_MEDIA_AUDIO`."
        category="Correctness"
        priority="8"
        summary="Affected by scoped storage"
        explanation="Scoped storage is enforced on Android 10+ (or Android 11+ if using `requestLegacyExternalStorage`). In particular, `WRITE_EXTERNAL_STORAGE` will no longer provide write access to all files; it will provide the equivalent of `READ_EXTERNAL_STORAGE` instead.&#xA;&#xA;As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:&#xA;* `android.permission.READ_MEDIA_IMAGES`&#xA;* `android.permission.READ_MEDIA_VIDEO`&#xA;* `android.permission.READ_MEDIA_AUDIO`&#xA;&#xA;and then add `maxSdkVersion=&quot;33&quot;` to the older permission. See the developer guide for how to do this: https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions&#xA;&#xA;The `MANAGE_EXTERNAL_STORAGE` permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at https://goo.gle/android-mediastore-createwriterequest.&#xA;&#xA;To learn more, read these resources: Play policy: https://goo.gle/policy-storage-help Allowable use cases: https://goo.gle/policy-storage-usecases"
        url="https://goo.gle/android-storage-usecases"
        urls="https://goo.gle/android-storage-usecases"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.READ_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml"
            line="18"
            column="36"/>
    </issue>

    <issue
        id="ScopedStorage"
        severity="Warning"
        message="WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the `MediaStore.createWriteRequest` intent."
        category="Correctness"
        priority="8"
        summary="Affected by scoped storage"
        explanation="Scoped storage is enforced on Android 10+ (or Android 11+ if using `requestLegacyExternalStorage`). In particular, `WRITE_EXTERNAL_STORAGE` will no longer provide write access to all files; it will provide the equivalent of `READ_EXTERNAL_STORAGE` instead.&#xA;&#xA;As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:&#xA;* `android.permission.READ_MEDIA_IMAGES`&#xA;* `android.permission.READ_MEDIA_VIDEO`&#xA;* `android.permission.READ_MEDIA_AUDIO`&#xA;&#xA;and then add `maxSdkVersion=&quot;33&quot;` to the older permission. See the developer guide for how to do this: https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions&#xA;&#xA;The `MANAGE_EXTERNAL_STORAGE` permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at https://goo.gle/android-mediastore-createwriterequest.&#xA;&#xA;To learn more, read these resources: Play policy: https://goo.gle/policy-storage-help Allowable use cases: https://goo.gle/policy-storage-usecases"
        url="https://goo.gle/android-storage-usecases"
        urls="https://goo.gle/android-storage-usecases"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.WRITE_EXTERNAL_STORAGE&quot; />"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml"
            line="19"
            column="36"/>
    </issue>

    <issue
        id="DefaultLocale"
        severity="Warning"
        message="Implicitly using the default locale is a common source of bugs: Use `String.format(Locale, ...)` instead"
        category="Correctness"
        priority="6"
        summary="Implied default locale in case conversion"
        explanation="Calling `String#toLowerCase()` or `#toUpperCase()` **without specifying an explicit locale** is a common source of bugs. The reason for that is that those methods will use the current locale on the user&apos;s device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for `i` is **not** `I`.&#xA;&#xA;If you want the methods to just perform ASCII replacement, for example to convert an enum name, call `String#toUpperCase(Locale.ROOT)` instead. If you really want to use the current locale, call `String#toUpperCase(Locale.getDefault())` instead."
        url="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        urls="https://developer.android.com/reference/java/util/Locale.html#default_locale"
        errorLine1="        return String.format("
        errorLine2="               ^">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\DeviceInfoHelper.kt"
            line="296"
            column="16"/>
    </issue>

    <issue
        id="NewApi"
        severity="Error"
        message="Call requires API level 26 (current min is 24): `android.content.ContextWrapper#startForegroundService`"
        category="Correctness"
        priority="6"
        summary="Calling new methods on older versions"
        explanation="This check scans through all the Android API calls in the application and warns about any calls that are not available on **all** versions targeted by this application (according to its minimum SDK attribute in the manifest).&#xA;&#xA;If you really want to use this API and don&apos;t need to support older devices just set the `minSdkVersion` in your `build.gradle` or `AndroidManifest.xml` files.&#xA;&#xA;If your code is **deliberately** accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the `@TargetApi` annotation specifying the local minimum SDK to apply, such as `@TargetApi(11)`, such that this check considers 11 rather than your manifest file&apos;s minimum SDK as the required API level.&#xA;&#xA;If you are deliberately setting `android:` attributes in style definitions, make sure you place this in a `values-v`*NN* folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.&#xA;&#xA;Similarly, you can use tools:targetApi=&quot;11&quot; in an XML file to indicate that the element will only be inflated in an adequate context."
        errorLine1="            startForegroundService(serviceIntent)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="240"
            column="13"/>
    </issue>

    <issue
        id="NotificationPermission"
        severity="Error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission"
        category="Correctness"
        priority="6"
        summary="Notifications Without Permission"
        explanation="When targeting Android 13 and higher, posting permissions requires holding the runtime permission `android.permission.POST_NOTIFICATIONS`."
        errorLine1="            notificationManager.notify(NOTIFICATION_ID, notification)"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\services\core\RemoteControlForegroundService.kt"
            line="392"
            column="13"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdk 34"
        errorLine2="        ~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build.gradle"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="BatteryLife"
        severity="Warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html"
        category="Correctness"
        priority="5"
        summary="Battery Life Issues"
        explanation="This issue flags code that either&#xA;* negatively affects battery life, or&#xA;* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.&#xA;&#xA;Generally, you should be using `WorkManager` instead.&#xA;&#xA;For more details on how to update your code, please see https://developer.android.com/topic/performance/background-optimization"
        url="https://developer.android.com/topic/performance/background-optimization"
        urls="https://developer.android.com/topic/performance/background-optimization"
        errorLine1="            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)"
        errorLine2="                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt"
            line="190"
            column="42"/>
    </issue>

    <issue
        id="ProtectedPermissions"
        severity="Error"
        message="Permission is only granted to system apps"
        category="Correctness"
        priority="5"
        summary="Using system app permission"
        explanation="Permissions with the protection level `signature`, `privileged` or `signatureOrSystem` are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions."
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.BIND_ACCESSIBILITY_SERVICE&quot; />"
        errorLine2="                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\AndroidManifest.xml"
            line="10"
            column="22"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.13.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.core:core-ktx:1.12.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build.gradle"
            line="38"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.8.3"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.lifecycle:lifecycle-runtime-ktx:2.7.0&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build.gradle"
            line="42"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;com.google.code.gson:gson:2.10.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\build.gradle"
            line="46"
            column="20"/>
    </issue>

    <issue
        id="SwitchIntDef"
        severity="Warning"
        message="Switch statement on an `int` with known associated constant missing case `AccessibilityEvent.TYPE_ANNOUNCEMENT`, `AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_END`, `AccessibilityEvent.TYPE_GESTURE_DETECTION_START`, `AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED`, `AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END`, `AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_END`, `AccessibilityEvent.TYPE_TOUCH_INTERACTION_START`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED`, `AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED`, `AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED`, `AccessibilityEvent.TYPE_VIEW_FOCUSED`, `AccessibilityEvent.TYPE_VIEW_HOVER_ENTER`, `AccessibilityEvent.TYPE_VIEW_HOVER_EXIT`, `AccessibilityEvent.TYPE_VIEW_LONG_CLICKED`, `AccessibilityEvent.TYPE_VIEW_SCROLLED`, `AccessibilityEvent.TYPE_VIEW_SELECTED`, `AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL`, `AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED`, `AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY`, `AccessibilityEvent.TYPE_WINDOWS_CHANGED`, `AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED`"
        category="Correctness"
        priority="3"
        summary="Missing @IntDef in Switch"
        explanation="This check warns if a `switch` statement does not explicitly include all the values declared by the typedef `@IntDef` declaration."
        errorLine1="            when (event.eventType) {"
        errorLine2="            ~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\services\core\RemoteAccessibilityService.kt"
            line="119"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return android.os.Build.VERSION.SDK_INT >= MIN_ANDROID_VERSION"
        errorLine2="               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\constants\AppConstants.kt"
            line="174"
            column="16"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\DeviceInfoHelper.kt"
            line="140"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            &quot;securityPatch&quot; to if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\DeviceInfoHelper.kt"
            line="236"
            column="36"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.ICE_CREAM_SANDWICH) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\ImageProcessor.kt"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\PermissionHelper.kt"
            line="134"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\PermissionHelper.kt"
            line="138"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="                android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\utils\PermissionHelper.kt"
            line="142"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt"
            line="47"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt"
            line="84"
            column="20"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt"
            line="153"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\managers\PermissionManager.kt"
            line="189"
            column="13"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 24"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\services\core\RemoteControlForegroundService.kt"
            line="366"
            column="17"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 21"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        &lt;item name=&quot;android:statusBarColor&quot; tools:targetApi=&quot;l&quot;>?attr/colorPrimaryVariant&lt;/item>"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\themes.xml"
            line="13"
            column="45"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        severity="Warning"
        message="Do not place Android context classes in static fields (static reference to `PasswordRecorder` which has field `context` pointing to `Context`); this is a memory leak"
        category="Performance"
        priority="6"
        summary="Static Field Leaks"
        explanation="A static field will leak contexts.&#xA;&#xA;Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a `Fragment` or `Activity`, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.&#xA;&#xA;Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.&#xA;&#xA;ViewModel classes should never point to Views or non-application Contexts."
        errorLine1="        @Volatile"
        errorLine2="        ^">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\services\automation\PasswordRecorder.kt"
            line="27"
            column="9"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme_RemoteAndroidControl`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/background_primary&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml"
            line="9"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        severity="Warning"
        message="Possible overdraw: Root element paints background `@color/background_primary` with a theme that also paints a background (inferred theme is `@style/Theme.RemoteAndroidControl`)"
        category="Performance"
        priority="3"
        summary="Overdraw: Painting regions more than once"
        explanation="If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called &quot;overdraw&quot;.&#xA;&#xA;NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it&apos;s currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.&#xA;&#xA;If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.&#xA;&#xA;Of course it&apos;s possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead."
        errorLine1="    android:background=&quot;@color/background_primary&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.purple_200` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorPrimary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorPrimary&quot;>#FF2196F3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="12"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorPrimaryDark` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorPrimaryDark&quot;>#FF1976D2&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.colorAccent` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;colorAccent&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.backgroundColor` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;backgroundColor&quot;>#FFF5F5F5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="17"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.cardBackgroundColor` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;cardBackgroundColor&quot;>#FFFFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="18"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.textColorPrimary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;textColorPrimary&quot;>#FF212121&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="23"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.textColorSecondary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;textColorSecondary&quot;>#FF757575&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="24"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.accent_secondary` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;accent_secondary&quot;>#FF03DAC5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\values\colors.xml"
            line="30"
            column="12"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="159"
            column="18"/>
    </issue>

    <issue
        id="ButtonStyle"
        severity="Warning"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        category="Usability"
        priority="5"
        summary="Button should be borderless"
        explanation="Button bars typically use a borderless style for the buttons. Set the `style=&quot;?android:attr/buttonBarButtonStyle&quot;` attribute on each of the buttons, and set `style=&quot;?android:attr/buttonBarStyle&quot;` on the parent layout"
        url="https://d.android.com/r/studio-ui/designer/material/dialogs"
        urls="https://d.android.com/r/studio-ui/designer/material/dialogs"
        errorLine1="                &lt;Button"
        errorLine2="                 ~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="171"
            column="18"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="        &lt;ImageView"
        errorLine2="         ~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml"
            line="20"
            column="10"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="            &lt;ImageView"
        errorLine2="             ~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="23"
            column="14"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.text = &quot;✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 正在启动屏幕录制...&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="58"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.text = &quot;✅ 基础权限已授予\n❌ 屏幕录制权限被拒绝\n请重新申请权限以启用完整功能&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="62"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.text = &quot;✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击&apos;开始录制&apos;将申请屏幕录制权限&quot;"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="144"
            column="35"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.text = &quot;✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击&apos;开始录制&apos;将申请屏幕录制权限&quot;"
        errorLine2="                                                            ~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="144"
            column="61"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.text = &quot;✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击&apos;开始录制&apos;将申请屏幕录制权限&quot;"
        errorLine2="                                                                             ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="144"
            column="78"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.text = &quot;✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击&apos;开始录制&apos;将申请屏幕录制权限&quot;"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="147"
            column="35"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.text = &quot;✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击&apos;开始录制&apos;将申请屏幕录制权限&quot;"
        errorLine2="                                                            ~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="147"
            column="61"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.text = &quot;✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击&apos;开始录制&apos;将申请屏幕录制权限&quot;"
        errorLine2="                                                                             ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="147"
            column="78"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.text = &quot;✅ 基础权限已授予\n🔄 正在申请屏幕录制权限...\n请在弹出的对话框中点击&apos;立即开始&apos;&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="211"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.text = &quot;✅ 基础权限已授予\n❌ 屏幕录制权限申请失败: ${e.message}&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="216"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.text = &quot;✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="244"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.text = &quot;✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...&quot;"
        errorLine2="                                                        ~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="244"
            column="57"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="            statusText.text = &quot;✅ 所有权限已授予\n❌ 服务启动失败: ${e.message}&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="246"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.text = &quot;🎯 PC端请求屏幕录制权限\n正在申请屏幕录制权限...&quot;"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="261"
            column="35"/>
    </issue>

    <issue
        id="SetTextI18n"
        severity="Warning"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        category="Internationalization"
        priority="6"
        summary="TextView Internationalization"
        explanation="When calling `TextView#setText`&#xA;* Never call `Number#toString()` to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using `String#format` with proper format specifications (`%d` or `%f`) instead.&#xA;* Do not pass a string literal (e.g. &quot;Hello&quot;) to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.&#xA;* Do not build messages by concatenating text chunks. Such messages can not be properly translated."
        url="https://developer.android.com/guide/topics/resources/localization.html"
        urls="https://developer.android.com/guide/topics/resources/localization.html"
        errorLine1="                statusText.text = &quot;🎯 PC端请求屏幕录制权限\n正在申请屏幕录制权限...&quot;"
        errorLine2="                                   ~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\java\com\remoteandroid\control\ui\activities\MainActivity.kt"
            line="261"
            column="36"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;RemoteAndroid Control&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;RemoteAndroid Control&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Android远程控制客户端&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="            android:text=&quot;Android远程控制客户端&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;正在初始化...&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;正在初始化...&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml"
            line="64"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;申请权限&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="        android:text=&quot;申请权限&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_main.xml"
            line="79"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;RemoteAndroid Control&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;RemoteAndroid Control&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="32"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;权限申请向导&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;权限申请向导&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="41"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;无障碍权限&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;无障碍权限&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="67"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;用于实现密码记录和自动化操作功能，这是应用的核心功能&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;用于实现密码记录和自动化操作功能，这是应用的核心功能&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="78"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;RemoteAndroid需要无障碍权限来监听屏幕上的点击事件，实现密码记录功能。此权限仅用于记录您的操作，不会收集任何个人隐私信息。&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;RemoteAndroid需要无障碍权限来监听屏幕上的点击事件，实现密码记录功能。此权限仅用于记录您的操作，不会收集任何个人隐私信息。&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="88"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;申请步骤&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;申请步骤&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="115"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;申请权限&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                android:text=&quot;申请权限&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="144"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;跳过&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;跳过&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="164"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;下一步&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="                    android:text=&quot;下一步&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\Desktop\xue\RemoteAndroid_Control_Suite\Android_Client\app\src\main\res\layout\activity_permission_guide.xml"
            line="176"
            column="21"/>
    </issue>

</issues>
