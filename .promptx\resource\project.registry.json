{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-04T00:24:29.986Z", "updatedAt": "2025-08-04T00:24:29.991Z", "resourceCount": 3}, "resources": [{"id": "remote-android-development", "source": "project", "protocol": "execution", "name": "Remote Android Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/remote-android-expert/execution/remote-android-development.execution.md", "metadata": {"createdAt": "2025-08-04T00:24:29.989Z", "updatedAt": "2025-08-04T00:24:29.989Z", "scannedAt": "2025-08-04T00:24:29.989Z", "path": "role/remote-android-expert/execution/remote-android-development.execution.md"}}, {"id": "remote-android-expert", "source": "project", "protocol": "role", "name": "Remote Android Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/remote-android-expert/remote-android-expert.role.md", "metadata": {"createdAt": "2025-08-04T00:24:29.990Z", "updatedAt": "2025-08-04T00:24:29.990Z", "scannedAt": "2025-08-04T00:24:29.990Z", "path": "role/remote-android-expert/remote-android-expert.role.md"}}, {"id": "remote-android-thinking", "source": "project", "protocol": "thought", "name": "Remote Android Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/remote-android-expert/thought/remote-android-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T00:24:29.990Z", "updatedAt": "2025-08-04T00:24:29.990Z", "scannedAt": "2025-08-04T00:24:29.990Z", "path": "role/remote-android-expert/thought/remote-android-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}