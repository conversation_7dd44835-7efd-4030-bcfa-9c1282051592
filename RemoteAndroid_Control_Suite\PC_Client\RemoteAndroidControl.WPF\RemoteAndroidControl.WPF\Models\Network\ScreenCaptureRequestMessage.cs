using Newtonsoft.Json;

namespace RemoteAndroidControl.WPF.Models.Network
{
    /// <summary>
    /// RemoteAndroid Control Suite - 屏幕录制请求消息
    /// PC端向Android端请求开始屏幕录制
    /// </summary>
    public class ScreenCaptureRequestMessage : MessageBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ScreenCaptureRequestMessage()
        {
            MessageType = MessageTypes.REQUEST_SCREEN_CAPTURE;
            Data = new ScreenCaptureRequestData();
        }

        /// <summary>
        /// 屏幕录制请求数据
        /// </summary>
        [JsonProperty("data")]
        public new ScreenCaptureRequestData Data { get; set; }
    }

    /// <summary>
    /// 屏幕录制请求数据结构
    /// </summary>
    public class ScreenCaptureRequestData
    {
        /// <summary>
        /// 请求类型：start/stop
        /// </summary>
        [JsonProperty("action")]
        public string Action { get; set; } = "start";

        /// <summary>
        /// 屏幕录制参数
        /// </summary>
        [JsonProperty("parameters")]
        public ScreenCaptureParameters Parameters { get; set; } = new();
    }

    /// <summary>
    /// 屏幕录制参数
    /// </summary>
    public class ScreenCaptureParameters
    {
        /// <summary>
        /// 屏幕宽度
        /// </summary>
        [JsonProperty("width")]
        public int Width { get; set; } = 720;

        /// <summary>
        /// 屏幕高度
        /// </summary>
        [JsonProperty("height")]
        public int Height { get; set; } = 1280;

        /// <summary>
        /// DPI
        /// </summary>
        [JsonProperty("dpi")]
        public int Dpi { get; set; } = 320;

        /// <summary>
        /// 帧率
        /// </summary>
        [JsonProperty("frameRate")]
        public int FrameRate { get; set; } = 15;

        /// <summary>
        /// JPEG质量
        /// </summary>
        [JsonProperty("quality")]
        public int Quality { get; set; } = 30;
    }
}
