using Newtonsoft.Json;

namespace RemoteAndroidControl.WPF.Models.Network
{
    /// <summary>
    /// RemoteAndroid Control Suite - 屏幕录制响应消息
    /// Android端向PC端响应屏幕录制请求结果
    /// </summary>
    public class ScreenCaptureResponseMessage : MessageBase
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ScreenCaptureResponseMessage()
        {
            MessageType = MessageTypes.SCREEN_CAPTURE_RESPONSE;
            Data = new ScreenCaptureResponseData();
        }

        /// <summary>
        /// 屏幕录制响应数据
        /// </summary>
        [JsonProperty("data")]
        public new ScreenCaptureResponseData Data { get; set; }
    }

    /// <summary>
    /// 屏幕录制响应数据结构
    /// </summary>
    public class ScreenCaptureResponseData
    {
        /// <summary>
        /// 请求是否成功
        /// </summary>
        [JsonProperty("success")]
        public bool Success { get; set; }

        /// <summary>
        /// 响应消息
        /// </summary>
        [JsonProperty("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 错误代码（如果失败）
        /// </summary>
        [JsonProperty("errorCode")]
        public string? ErrorCode { get; set; }

        /// <summary>
        /// 屏幕录制状态
        /// </summary>
        [JsonProperty("captureStatus")]
        public string CaptureStatus { get; set; } = "stopped"; // started/stopped/permission_denied/error
    }
}
