using System;
using System.IO;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using RemoteAndroidControl.WPF.ViewModels;
using RemoteAndroidControl.WPF.Services.Network;
using RemoteAndroidControl.WPF.Models.Network;

namespace RemoteAndroidControl.WPF.ViewModels.Modules
{
    /// <summary>
    /// 屏幕控制视图模型
    /// 管理屏幕显示和控制相关的数据和命令
    /// </summary>
    public partial class ScreenControlViewModel : BaseViewModel
    {
        private readonly ConnectionManager _connectionManager;
        private DateTime _lastFrameTime = DateTime.Now;
        private int _frameCount = 0;

        [ObservableProperty]
        private BitmapImage? screenImage;

        [ObservableProperty]
        private bool isConnected;

        [ObservableProperty]
        private bool isCapturing;

        [ObservableProperty]
        private string connectionStatus = "未连接";

        [ObservableProperty]
        private string deviceInfo = "(未连接)";

        [ObservableProperty]
        private int currentFps;

        [ObservableProperty]
        private double currentDelay;

        [ObservableProperty]
        private string resolution = "--";

        [ObservableProperty]
        private int screenWidth;

        [ObservableProperty]
        private int screenHeight;

        [ObservableProperty]
        private double imageQuality = 30.0;

        [ObservableProperty]
        private int targetFrameRate = 15;

        [ObservableProperty]
        private bool showPerformanceInfo = true;

        [ObservableProperty]
        private bool enableClickFeedback = true;

        public ScreenControlViewModel()
        {
            Title = "屏幕控制";
            _connectionManager = ConnectionManager.Instance;
            InitializeCommands();
            SubscribeToEvents();
        }
        
        /// <summary>
        /// 初始化命令
        /// </summary>
        private void InitializeCommands()
        {
            StartCaptureCommand = new RelayCommand(StartCapture, CanStartCapture);
            StopCaptureCommand = new RelayCommand(StopCapture, CanStopCapture);
            OpenSettingsCommand = new RelayCommand(OpenSettings);
            RefreshConnectionCommand = new RelayCommand(RefreshConnection);
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        private void SubscribeToEvents()
        {
            _connectionManager.ScreenDataReceived += OnScreenDataReceived;
            _connectionManager.ClientConnected += OnClientConnected;
            _connectionManager.ClientDisconnected += OnClientDisconnected;
            _connectionManager.ConnectionStatusChanged += OnConnectionStatusChanged;
        }
        
        /// <summary>
        /// 开始录制命令
        /// </summary>
        public ICommand StartCaptureCommand { get; private set; } = null!;

        /// <summary>
        /// 停止录制命令
        /// </summary>
        public ICommand StopCaptureCommand { get; private set; } = null!;

        /// <summary>
        /// 打开设置命令
        /// </summary>
        public ICommand OpenSettingsCommand { get; private set; } = null!;

        /// <summary>
        /// 刷新连接命令
        /// </summary>
        public ICommand RefreshConnectionCommand { get; private set; } = null!;
        
        /// <summary>
        /// 开始录制
        /// </summary>
        private async void StartCapture()
        {
            if (!IsConnected)
                return;

            IsCapturing = true;
            SetBusy(true, "正在启动屏幕录制...");
            ConnectionStatus = "正在请求屏幕录制权限...";

            try
            {
                // 发送屏幕录制请求到Android端
                await _connectionManager.SendScreenCaptureRequestAsync();
                ConnectionStatus = "屏幕录制请求已发送，等待Android端授权...";
            }
            catch (Exception ex)
            {
                SetError($"发送屏幕录制请求失败: {ex.Message}");
                IsCapturing = false;
                SetBusy(false);
                ConnectionStatus = "屏幕录制请求失败";
            }
        }
        
        /// <summary>
        /// 停止录制
        /// </summary>
        private void StopCapture()
        {
            IsCapturing = false;
            SetBusy(false);
            
            // 清空屏幕图像
            ScreenImage = null;
            
            // 这里会通过事件或服务通知View层执行实际的停止操作
            OnStopCaptureRequested?.Invoke();
        }
        
        /// <summary>
        /// 打开设置
        /// </summary>
        private void OpenSettings()
        {
            OnSettingsRequested?.Invoke();
        }
        
        /// <summary>
        /// 刷新连接
        /// </summary>
        private void RefreshConnection()
        {
            OnRefreshConnectionRequested?.Invoke();
        }
        
        /// <summary>
        /// 检查是否可以开始录制
        /// </summary>
        private bool CanStartCapture()
        {
            return IsConnected && !IsCapturing;
        }
        
        /// <summary>
        /// 检查是否可以停止录制
        /// </summary>
        private bool CanStopCapture()
        {
            return IsCapturing;
        }
        
        /// <summary>
        /// 更新连接状态
        /// </summary>
        public void UpdateConnectionStatus(bool connected)
        {
            IsConnected = connected;
            ConnectionStatus = connected ? "已连接" : "未连接";
            
            if (!connected)
            {
                IsCapturing = false;
                ScreenImage = null;
                DeviceInfo = "(未连接)";
                Resolution = "--";
                CurrentFps = 0;
                CurrentDelay = 0;
            }
            
            // 更新命令状态
            ((RelayCommand)StartCaptureCommand).NotifyCanExecuteChanged();
            ((RelayCommand)StopCaptureCommand).NotifyCanExecuteChanged();
        }
        
        /// <summary>
        /// 更新设备信息
        /// </summary>
        public void UpdateDeviceInfo(string model, string androidVersion)
        {
            DeviceInfo = $"({model} - Android {androidVersion})";
        }
        
        /// <summary>
        /// 更新屏幕参数
        /// </summary>
        public void UpdateScreenParameters(int width, int height)
        {
            ScreenWidth = width;
            ScreenHeight = height;
            Resolution = $"{width}x{height}";
        }
        
        /// <summary>
        /// 更新性能信息
        /// </summary>
        public void UpdatePerformanceInfo(int fps, double delay)
        {
            CurrentFps = fps;
            CurrentDelay = delay;
        }
        
        /// <summary>
        /// 更新屏幕图像
        /// </summary>
        public void UpdateScreenImage(BitmapImage image)
        {
            ScreenImage = image;
            SetBusy(false);
        }
        
        /// <summary>
        /// 获取格式化的性能信息
        /// </summary>
        public string GetPerformanceInfo()
        {
            return $"FPS: {CurrentFps} | 延迟: {CurrentDelay:F0}ms | 分辨率: {Resolution}";
        }
        
        /// <summary>
        /// 获取连接状态颜色
        /// </summary>
        public string GetConnectionStatusColor()
        {
            return IsConnected ? "#FF32CD32" : "#FFDC143C"; // LimeGreen : Crimson
        }
        
        #region 事件处理

        /// <summary>
        /// 屏幕数据接收事件处理
        /// </summary>
        private void OnScreenDataReceived(object? sender, ScreenDataMessage message)
        {
            try
            {
                if (message?.Data?.ImageData != null)
                {
                    // 解码Base64图像数据
                    var imageBytes = Convert.FromBase64String(message.Data.ImageData);

                    // 创建BitmapImage
                    var bitmap = new BitmapImage();
                    using (var stream = new MemoryStream(imageBytes))
                    {
                        bitmap.BeginInit();
                        bitmap.CacheOption = BitmapCacheOption.OnLoad;
                        bitmap.StreamSource = stream;
                        bitmap.EndInit();
                        bitmap.Freeze(); // 使其可以跨线程访问
                    }

                    // 更新UI（需要在UI线程上执行）
                    System.Windows.Application.Current.Dispatcher.Invoke(() =>
                    {
                        ScreenImage = bitmap;
                        ScreenWidth = message.Data.Width;
                        ScreenHeight = message.Data.Height;
                        Resolution = $"{message.Data.Width}x{message.Data.Height}";

                        // 更新性能统计
                        UpdatePerformanceStats();

                        // 更新状态
                        if (!IsCapturing)
                        {
                            IsCapturing = true;
                            ConnectionStatus = "屏幕录制中";
                        }

                        SetBusy(false);
                    });
                }
            }
            catch (Exception ex)
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    SetError($"处理屏幕数据失败: {ex.Message}");
                });
            }
        }

        /// <summary>
        /// 客户端连接事件处理
        /// </summary>
        private void OnClientConnected(object? sender, EventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                IsConnected = true;
                ConnectionStatus = "已连接";
                DeviceInfo = "Android设备";
                ClearError();
            });
        }

        /// <summary>
        /// 客户端断开连接事件处理
        /// </summary>
        private void OnClientDisconnected(object? sender, EventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                IsConnected = false;
                IsCapturing = false;
                ConnectionStatus = "连接断开";
                DeviceInfo = "(未连接)";
                ScreenImage = null;
                SetBusy(false);
            });
        }

        /// <summary>
        /// 连接状态变化事件处理
        /// </summary>
        private void OnConnectionStatusChanged(object? sender, ConnectionStatusChangedEventArgs e)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                ConnectionStatus = e.IsConnected ? $"已连接 ({e.ClientEndPoint})" : "连接断开";
            });
        }

        /// <summary>
        /// 更新性能统计
        /// </summary>
        private void UpdatePerformanceStats()
        {
            _frameCount++;
            var now = DateTime.Now;
            var elapsed = (now - _lastFrameTime).TotalSeconds;

            if (elapsed >= 1.0) // 每秒更新一次FPS
            {
                CurrentFps = (int)(_frameCount / elapsed);
                _frameCount = 0;
                _lastFrameTime = now;
            }

            // 计算延迟（这里是简化版本）
            CurrentDelay = 50; // 实际应该基于时间戳计算
        }

        #endregion

        // 事件定义
        public event Action? OnStartCaptureRequested;
        public event Action? OnStopCaptureRequested;
        public event Action? OnSettingsRequested;
        public event Action? OnRefreshConnectionRequested;

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Cleanup()
        {
            // 取消事件订阅
            if (_connectionManager != null)
            {
                _connectionManager.ScreenDataReceived -= OnScreenDataReceived;
                _connectionManager.ClientConnected -= OnClientConnected;
                _connectionManager.ClientDisconnected -= OnClientDisconnected;
                _connectionManager.ConnectionStatusChanged -= OnConnectionStatusChanged;
            }

            ScreenImage = null;
            OnStartCaptureRequested = null;
            OnStopCaptureRequested = null;
            OnSettingsRequested = null;
            OnRefreshConnectionRequested = null;
        }
    }
}
