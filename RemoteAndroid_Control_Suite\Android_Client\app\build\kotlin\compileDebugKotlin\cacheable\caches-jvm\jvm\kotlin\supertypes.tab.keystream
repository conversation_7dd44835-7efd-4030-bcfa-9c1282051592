5com.remoteandroid.control.models.network.ClickMessage8com.remoteandroid.control.models.network.PasswordMessage:com.remoteandroid.control.models.network.ScreenDataMessageIcom.remoteandroid.control.services.automation.GestureExecutor.GestureTypeBcom.remoteandroid.control.services.core.RemoteAccessibilityServiceFcom.remoteandroid.control.services.core.RemoteControlForegroundService<com.remoteandroid.control.services.core.ScreenCaptureService4com.remoteandroid.control.ui.activities.MainActivity?com.remoteandroid.control.ui.activities.PermissionGuideActivityDcom.remoteandroid.control.models.network.ScreenCaptureRequestMessageEcom.remoteandroid.control.models.network.ScreenCaptureResponseMessage                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     