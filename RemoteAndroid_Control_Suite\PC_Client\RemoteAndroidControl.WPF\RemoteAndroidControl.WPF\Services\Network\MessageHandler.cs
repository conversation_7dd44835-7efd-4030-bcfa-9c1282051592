using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using RemoteAndroidControl.WPF.Models.Network;

namespace RemoteAndroidControl.WPF.Services.Network
{
    /// <summary>
    /// RemoteAndroid Control Suite - 消息处理器
    /// 处理来自Android客户端的各种消息类型
    /// </summary>
    public class MessageHandler
    {
        #region 字段和属性

        private readonly ILogger<MessageHandler>? _logger;

        #endregion

        #region 事件

        /// <summary>
        /// 屏幕数据接收事件
        /// </summary>
        public event EventHandler<ScreenDataReceivedEventArgs>? ScreenDataReceived;

        /// <summary>
        /// 心跳消息接收事件
        /// </summary>
        public event EventHandler<HeartbeatReceivedEventArgs>? HeartbeatReceived;

        /// <summary>
        /// 连接响应接收事件
        /// </summary>
        public event EventHandler<ConnectResponseReceivedEventArgs>? ConnectResponseReceived;

        /// <summary>
        /// 屏幕录制响应接收事件
        /// </summary>
        public event EventHandler<ScreenCaptureResponseReceivedEventArgs>? ScreenCaptureResponseReceived;

        #endregion

        #region 构造函数

        public MessageHandler(ILogger<MessageHandler>? logger = null)
        {
            _logger = logger;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 处理接收到的消息
        /// </summary>
        /// <param name="message">消息对象</param>
        /// <param name="rawJson">原始JSON字符串</param>
        /// <returns>处理任务</returns>
        public async Task HandleMessageAsync(MessageBase message, string rawJson)
        {
            try
            {
                switch (message.MessageType)
                {
                    case MessageTypes.SCREEN_DATA:
                        await HandleScreenDataAsync(rawJson);
                        break;

                    case MessageTypes.HEARTBEAT:
                        await HandleHeartbeatAsync(message);
                        break;

                    case MessageTypes.CONNECT_RESPONSE:
                        await HandleConnectResponseAsync(rawJson);
                        break;

                    case MessageTypes.ERROR:
                        await HandleErrorAsync(message);
                        break;

                    case MessageTypes.SCREEN_CAPTURE_RESPONSE:
                        await HandleScreenCaptureResponseAsync(rawJson);
                        break;

                    default:
                        _logger?.LogWarning($"未知消息类型: {message.MessageType}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"处理消息时发生错误: {message.MessageType}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 处理屏幕数据消息
        /// </summary>
        /// <param name="rawJson">原始JSON</param>
        /// <returns>处理任务</returns>
        private async Task HandleScreenDataAsync(string rawJson)
        {
            try
            {
                Console.WriteLine($"[DEBUG] 开始处理屏幕数据消息，JSON长度: {rawJson.Length}");

                var screenMessage = MessageBase.FromJson<ScreenDataMessage>(rawJson);
                if (screenMessage?.Data != null)
                {
                    Console.WriteLine($"[DEBUG] 屏幕数据解析成功: {screenMessage.Data.Width}x{screenMessage.Data.Height}, 图像数据长度: {screenMessage.Data.ImageData?.Length ?? 0}");
                    OnScreenDataReceived(new ScreenDataReceivedEventArgs(screenMessage.Data, screenMessage));
                    _logger?.LogDebug($"处理屏幕数据: {screenMessage.Data.Width}x{screenMessage.Data.Height}");
                }
                else
                {
                    Console.WriteLine("[DEBUG] 屏幕数据解析失败，screenMessage或Data为null");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] 处理屏幕数据异常: {ex.Message}");
                _logger?.LogError(ex, "处理屏幕数据消息失败");
            }
        }

        /// <summary>
        /// 处理心跳消息
        /// </summary>
        /// <param name="message">心跳消息</param>
        /// <returns>处理任务</returns>
        private async Task HandleHeartbeatAsync(MessageBase message)
        {
            try
            {
                OnHeartbeatReceived(new HeartbeatReceivedEventArgs(message.Timestamp));
                _logger?.LogDebug("收到心跳消息");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "处理心跳消息失败");
            }
        }

        /// <summary>
        /// 处理连接响应消息
        /// </summary>
        /// <param name="rawJson">原始JSON</param>
        /// <returns>处理任务</returns>
        private async Task HandleConnectResponseAsync(string rawJson)
        {
            try
            {
                // TODO: 解析连接响应数据
                OnConnectResponseReceived(new ConnectResponseReceivedEventArgs(true, "连接成功"));
                _logger?.LogInformation("收到连接响应");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "处理连接响应消息失败");
            }
        }

        /// <summary>
        /// 处理错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>处理任务</returns>
        private async Task HandleErrorAsync(MessageBase message)
        {
            try
            {
                string errorMsg = message.Data?.ToString() ?? "未知错误";
                _logger?.LogError($"收到客户端错误消息: {errorMsg}");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "处理错误消息失败");
            }
        }

        /// <summary>
        /// 处理屏幕录制响应消息
        /// </summary>
        /// <param name="rawJson">原始JSON</param>
        /// <returns>处理任务</returns>
        private async Task HandleScreenCaptureResponseAsync(string rawJson)
        {
            try
            {
                var response = MessageBase.FromJson<ScreenCaptureResponseMessage>(rawJson);
                if (response?.Data != null)
                {
                    OnScreenCaptureResponseReceived(new ScreenCaptureResponseReceivedEventArgs(
                        response.Data.Success,
                        response.Data.Message,
                        response.Data.CaptureStatus
                    ));

                    _logger?.LogInformation($"收到屏幕录制响应: {response.Data.Message}, 状态: {response.Data.CaptureStatus}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "处理屏幕录制响应消息失败");
            }
        }

        #endregion

        #region 事件触发方法

        protected virtual void OnScreenDataReceived(ScreenDataReceivedEventArgs e)
        {
            ScreenDataReceived?.Invoke(this, e);
        }

        protected virtual void OnHeartbeatReceived(HeartbeatReceivedEventArgs e)
        {
            HeartbeatReceived?.Invoke(this, e);
        }

        protected virtual void OnConnectResponseReceived(ConnectResponseReceivedEventArgs e)
        {
            ConnectResponseReceived?.Invoke(this, e);
        }

        protected virtual void OnScreenCaptureResponseReceived(ScreenCaptureResponseReceivedEventArgs e)
        {
            ScreenCaptureResponseReceived?.Invoke(this, e);
        }

        #endregion
    }

    #region 事件参数类

    /// <summary>
    /// 屏幕数据接收事件参数
    /// </summary>
    public class ScreenDataReceivedEventArgs : EventArgs
    {
        public ScreenData ScreenData { get; }
        public ScreenDataMessage Message { get; }

        public ScreenDataReceivedEventArgs(ScreenData screenData, ScreenDataMessage message)
        {
            ScreenData = screenData;
            Message = message;
        }
    }

    /// <summary>
    /// 心跳接收事件参数
    /// </summary>
    public class HeartbeatReceivedEventArgs : EventArgs
    {
        public long Timestamp { get; }

        public HeartbeatReceivedEventArgs(long timestamp)
        {
            Timestamp = timestamp;
        }
    }

    /// <summary>
    /// 连接响应接收事件参数
    /// </summary>
    public class ConnectResponseReceivedEventArgs : EventArgs
    {
        public bool Success { get; }
        public string Message { get; }

        public ConnectResponseReceivedEventArgs(bool success, string message)
        {
            Success = success;
            Message = message;
        }
    }

    /// <summary>
    /// 屏幕录制响应接收事件参数
    /// </summary>
    public class ScreenCaptureResponseReceivedEventArgs : EventArgs
    {
        public bool Success { get; }
        public string Message { get; }
        public string CaptureStatus { get; }

        public ScreenCaptureResponseReceivedEventArgs(bool success, string message, string captureStatus)
        {
            Success = success;
            Message = message;
            CaptureStatus = captureStatus;
        }
    }

    #endregion
}
