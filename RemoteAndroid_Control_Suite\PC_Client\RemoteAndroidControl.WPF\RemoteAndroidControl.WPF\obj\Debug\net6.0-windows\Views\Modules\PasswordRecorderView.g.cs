﻿#pragma checksum "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3DFB4BB840BFDD3AED3AEB0DE1D15F1C30AA82EA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using RemoteAndroidControl.WPF.Views.Modules;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace RemoteAndroidControl.WPF.Views.Modules {
    
    
    /// <summary>
    /// PasswordRecorderView
    /// </summary>
    public partial class PasswordRecorderView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse ConnectionIndicator;
        
        #line default
        #line hidden
        
        
        #line 31 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 37 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartListeningButton;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopListeningButton;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddTestButton;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearAllButton;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCountText;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AlipayCountText;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WeChatCountText;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LockScreenCountText;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ListeningStatusText;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PasswordDataGrid;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EmptyStatePanel;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusMessageText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/RemoteAndroidControl.WPF;component/views/modules/passwordrecorderview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Modules\PasswordRecorderView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ConnectionIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 2:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.StartListeningButton = ((System.Windows.Controls.Button)(target));
            return;
            case 4:
            this.StopListeningButton = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.AddTestButton = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.ClearAllButton = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.TotalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.AlipayCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.WeChatCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.LockScreenCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ListeningStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PasswordDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 13:
            this.EmptyStatePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 14:
            this.StatusMessageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

