{"logs": [{"outputFile": "com.remoteandroid.control.app-mergeReleaseResources-43:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\7d5087baff1a4001a5b77a578bce78ed\\transformed\\material-1.11.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1071,1164,1229,1288,1375,1437,1499,1559,1625,1687,1741,1849,1906,1967,2022,2093,2213,2304,2390,2538,2624,2710,2838,2926,3004,3057,3108,3174,3245,3323,3406,3485,3558,3634,3707,3778,3885,3977,4050,4140,4233,4307,4378,4469,4521,4601,4669,4753,4838,4900,4964,5027,5099,5203,5311,5407,5513,5570,5625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "274,354,433,520,612,699,802,918,1001,1066,1159,1224,1283,1370,1432,1494,1554,1620,1682,1736,1844,1901,1962,2017,2088,2208,2299,2385,2533,2619,2705,2833,2921,2999,3052,3103,3169,3240,3318,3401,3480,3553,3629,3702,3773,3880,3972,4045,4135,4228,4302,4373,4464,4516,4596,4664,4748,4833,4895,4959,5022,5094,5198,5306,5402,5508,5565,5620,5706"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,51,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3024,3104,3183,3270,3362,4192,4295,4411,4764,4829,4922,5153,5212,5299,5361,5423,5483,5549,5611,5665,5773,5830,5891,5946,6017,6137,6228,6314,6462,6548,6634,6762,6850,6928,6981,7032,7098,7169,7247,7330,7409,7482,7558,7631,7702,7809,7901,7974,8064,8157,8231,8302,8393,8445,8525,8593,8677,8762,8824,8888,8951,9023,9127,9235,9331,9437,9494,9854", "endLines": "5,33,34,35,36,37,45,46,47,51,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,116", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "324,3099,3178,3265,3357,3444,4290,4406,4489,4824,4917,4982,5207,5294,5356,5418,5478,5544,5606,5660,5768,5825,5886,5941,6012,6132,6223,6309,6457,6543,6629,6757,6845,6923,6976,7027,7093,7164,7242,7325,7404,7477,7553,7626,7697,7804,7896,7969,8059,8152,8226,8297,8388,8440,8520,8588,8672,8757,8819,8883,8946,9018,9122,9230,9326,9432,9489,9544,9935"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\46addebd337d7ba2893b2181119dd40d\\transformed\\core-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "38,39,40,41,42,43,44,119", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3544,3646,3743,3853,3959,4077,10093", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3539,3641,3738,3848,3954,4072,4187,10189"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\2d35c5093cb170b93636a303cf46f35a\\transformed\\ui-1.0.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,458,541,628,713,779,846,918,984", "endColumns": "89,83,95,82,82,86,84,65,66,71,65,119", "endOffsets": "190,274,370,453,536,623,708,774,841,913,979,1099"}, "to": {"startLines": "48,49,50,54,55,112,113,114,115,118,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4494,4584,4668,4987,5070,9549,9636,9721,9787,10021,10194,10260", "endColumns": "89,83,95,82,82,86,84,65,66,71,65,119", "endOffsets": "4579,4663,4759,5065,5148,9631,9716,9782,9849,10088,10255,10375"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.3\\transforms\\a1370fee3db5402869b59d38e9f7680e\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,9940", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,10016"}}]}]}