<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.7.0" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="18"
            column="36"
            startOffset="770"
            endLine="18"
            endColumn="76"
            endOffset="810"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="19"
            column="36"
            startOffset="850"
            endLine="19"
            endColumn="77"
            endOffset="891"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="error"
        message="">
        <fix-data minSdk="ffffffffff800000" requiresApi="fffffffffe000000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/ui/activities/MainActivity.kt"
            line="240"
            column="13"
            startOffset="8281"
            endLine="240"
            endColumn="35"
            endOffset="8303"/>
        <map>
            <entry
                name="desc"
                string="(Landroid.content.Intent;)"/>
            <entry
                name="message"
                string="Call requires API level 26 (current min is %1$s): `android.content.ContextWrapper#startForegroundService`"/>
            <api-levels id="minSdk"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="startForegroundService"/>
            <entry
                name="owner"
                string="android.content.ContextWrapper"/>
            <api-levels id="requiresApi"
                value="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/services/core/RemoteControlForegroundService.kt"
            line="392"
            column="13"
            startOffset="12887"
            endLine="392"
            endColumn="70"
            endOffset="12944"/>
    </incident>

    <incident
        id="BatteryLife"
        severity="warning"
        message="Use of `REQUEST_IGNORE_BATTERY_OPTIMIZATIONS` violates the Play Store Content Policy regarding acceptable use cases, as described in https://developer.android.com/training/monitoring-device-state/doze-standby.html">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/remoteandroid/control/managers/PermissionManager.kt"
            line="190"
            column="42"
            startOffset="6247"
            endLine="190"
            endColumn="85"
            endOffset="6290"/>
        <map>
            <condition targetGE="23"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_screen_share.xml"
            line="6"
            column="19"
            startOffset="199"
            endLine="6"
            endColumn="39"
            endOffset="219"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_screen_share.xml"
            line="8"
            column="26"
            startOffset="255"
            endLine="8"
            endColumn="46"
            endOffset="275"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_screen_share.xml"
            line="11"
            column="26"
            startOffset="463"
            endLine="11"
            endColumn="46"
            endOffset="483"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_screen_share.xml"
            line="14"
            column="26"
            startOffset="558"
            endLine="14"
            endColumn="46"
            endOffset="578"/>
        <map>
            <entry
                name="containsFillType"
                boolean="false"/>
            <entry
                name="containsGradient"
                boolean="false"/>
            <entry
                name="folderVersion"
                int="-1"/>
            <entry
                name="message"
                string="Resource references will not work correctly in images generated for this vector icon for API &lt; %1$d; check generated icon to make sure it looks acceptable"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 31">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
                startOffset="1427"
                endOffset="1447"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="33"
            column="9"
            startOffset="1427"
            endLine="33"
            endColumn="29"
            endOffset="1447"/>
        <map>
            <condition minGE="ffffffffc0000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete tools:targetApi"
            replacement=""
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
                startOffset="697"
                endOffset="716"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/themes.xml"
            line="13"
            column="45"
            startOffset="697"
            endLine="13"
            endColumn="64"
            endOffset="716"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

</incidents>
