<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 5 errors and 66 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Mon Aug 04 07:32:40 CST 2025 by AGP (8.7.0)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#MissingClass"><i class="material-icons error-icon">error</i>Missing registered class (2)</a>
      <a class="mdl-navigation__link" href="#ScopedStorage"><i class="material-icons warning-icon">warning</i>Affected by scoped storage (2)</a>
      <a class="mdl-navigation__link" href="#DefaultLocale"><i class="material-icons warning-icon">warning</i>Implied default locale in case conversion (1)</a>
      <a class="mdl-navigation__link" href="#NewApi"><i class="material-icons error-icon">error</i>Calling new methods on older versions (1)</a>
      <a class="mdl-navigation__link" href="#NotificationPermission"><i class="material-icons error-icon">error</i>Notifications Without Permission (1)</a>
      <a class="mdl-navigation__link" href="#OldTargetApi"><i class="material-icons warning-icon">warning</i>Target SDK attribute is not targeting latest version (1)</a>
      <a class="mdl-navigation__link" href="#BatteryLife"><i class="material-icons warning-icon">warning</i>Battery Life Issues (1)</a>
      <a class="mdl-navigation__link" href="#ProtectedPermissions"><i class="material-icons error-icon">error</i>Using system app permission (1)</a>
      <a class="mdl-navigation__link" href="#GradleDependency"><i class="material-icons warning-icon">warning</i>Obsolete Gradle Dependency (3)</a>
      <a class="mdl-navigation__link" href="#SwitchIntDef"><i class="material-icons warning-icon">warning</i>Missing @IntDef in Switch (1)</a>
      <a class="mdl-navigation__link" href="#ObsoleteSdkInt"><i class="material-icons warning-icon">warning</i>Obsolete SDK_INT Version Check (13)</a>
      <a class="mdl-navigation__link" href="#StaticFieldLeak"><i class="material-icons warning-icon">warning</i>Static Field Leaks (1)</a>
      <a class="mdl-navigation__link" href="#Overdraw"><i class="material-icons warning-icon">warning</i>Overdraw: Painting regions more than once (2)</a>
      <a class="mdl-navigation__link" href="#UnusedResources"><i class="material-icons warning-icon">warning</i>Unused resources (9)</a>
      <a class="mdl-navigation__link" href="#ButtonStyle"><i class="material-icons warning-icon">warning</i>Button should be borderless (2)</a>
      <a class="mdl-navigation__link" href="#ContentDescription"><i class="material-icons warning-icon">warning</i>Image without <code>contentDescription</code> (2)</a>
      <a class="mdl-navigation__link" href="#SetTextI18n"><i class="material-icons warning-icon">warning</i>TextView Internationalization (15)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (13)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#MissingClass">MissingClass</a>: Missing registered class</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ScopedStorage">ScopedStorage</a>: Affected by scoped storage</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DefaultLocale">DefaultLocale</a>: Implied default locale in case conversion</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#NewApi">NewApi</a>: Calling new methods on older versions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#NotificationPermission">NotificationPermission</a>: Notifications Without Permission</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#OldTargetApi">OldTargetApi</a>: Target SDK attribute is not targeting latest version</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#BatteryLife">BatteryLife</a>: Battery Life Issues</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#ProtectedPermissions">ProtectedPermissions</a>: Using system app permission</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#GradleDependency">GradleDependency</a>: Obsolete Gradle Dependency</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SwitchIntDef">SwitchIntDef</a>: Missing @IntDef in Switch</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">13</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ObsoleteSdkInt">ObsoleteSdkInt</a>: Obsolete SDK_INT Version Check</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#StaticFieldLeak">StaticFieldLeak</a>: Static Field Leaks</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#Overdraw">Overdraw</a>: Overdraw: Painting regions more than once</td></tr>
<tr>
<td class="countColumn">9</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedResources">UnusedResources</a>: Unused resources</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability">Usability</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ButtonStyle">ButtonStyle</a>: Button should be borderless</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Accessibility">Accessibility</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ContentDescription">ContentDescription</a>: Image without <code>contentDescription</code></td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">15</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SetTextI18n">SetTextI18n</a>: TextView Internationalization</td></tr>
<tr>
<td class="countColumn">13</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#ExtraIssues">Included Additional Checks (39)</a>
</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (41)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="MissingClass"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingClassCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing registered class</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:53</span>: <span class="message">Class referenced in the manifest, <code>com.remoteandroid.control.ui.activities.ScreenMaskActivity</code>, was not found in the project or the libraries</span><br /><pre class="errorlines">
<span class="lineno"> 50 </span>            
<span class="lineno"> 51 </span>        <span class="comment">&lt;!-- 屏幕遮蔽Activity --></span>
<span class="lineno"> 52 </span>        <span class="tag">&lt;activity</span><span class="attribute">
</span><span class="caretline"><span class="lineno"> 53 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="error"><span class="value">.ui.activities.ScreenMaskActivity</span></span><span class="value">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 54 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"false"</span> />
<span class="lineno"> 55 </span>
<span class="lineno"> 56 </span>        <span class="comment">&lt;!-- 无障碍服务 --></span></pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:82</span>: <span class="message">Class referenced in the manifest, <code>com.remoteandroid.control.services.core.FileService</code>, was not found in the project or the libraries</span><br /><pre class="errorlines">
<span class="lineno"> 79 </span>
<span class="lineno"> 80 </span>        <span class="comment">&lt;!-- 文件服务 --></span>
<span class="lineno"> 81 </span>        <span class="tag">&lt;service</span><span class="attribute">
</span><span class="caretline"><span class="lineno"> 82 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="error"><span class="value">.services.core.FileService</span></span><span class="value">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 83 </span>            <span class="prefix">android:</span><span class="attribute">exported</span>=<span class="value">"false"</span> />
<span class="lineno"> 84 </span>
<span class="lineno"> 85 </span>    <span class="tag">&lt;/application></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMissingClass" style="display: none;">
If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references properly.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div>To suppress this error, use the issue id "MissingClass" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MissingClass</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMissingClassLink" onclick="reveal('explanationMissingClass');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingClassCardLink" onclick="hideid('MissingClassCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ScopedStorage"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ScopedStorageCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Affected by scoped storage</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:18</span>: <span class="message">READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: <code>READ_MEDIA_IMAGES</code>, <code>READ_MEDIA_VIDEO</code> or <code>READ_MEDIA_AUDIO</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.SYSTEM_ALERT_WINDOW"</span> />
<span class="lineno"> 16 </span>    
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- 文件访问权限 --></span>
<span class="caretline"><span class="lineno"> 18 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.READ_EXTERNAL_STORAGE</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_EXTERNAL_STORAGE"</span> />
<span class="lineno"> 20 </span>    
<span class="lineno"> 21 </span>    <span class="comment">&lt;!-- 电池优化权限 --></span></pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:19</span>: <span class="message">WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the <code>MediaStore.createWriteRequest</code> intent.</span><br /><pre class="errorlines">
<span class="lineno"> 16 </span>    
<span class="lineno"> 17 </span>    <span class="comment">&lt;!-- 文件访问权限 --></span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_EXTERNAL_STORAGE"</span> />
<span class="caretline"><span class="lineno"> 19 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.WRITE_EXTERNAL_STORAGE</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 20 </span>    
<span class="lineno"> 21 </span>    <span class="comment">&lt;!-- 电池优化权限 --></span>
<span class="lineno"> 22 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"</span> />
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationScopedStorage" style="display: none;">
Scoped storage is enforced on Android 10+ (or Android 11+ if using <code>requestLegacyExternalStorage</code>). In particular, <code>WRITE_EXTERNAL_STORAGE</code> will no longer provide write access to all files; it will provide the equivalent of <code>READ_EXTERNAL_STORAGE</code> instead.<br/>
<br/>
As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:<br/>
* <code>android.permission.READ_MEDIA_IMAGES</code><br/>
* <code>android.permission.READ_MEDIA_VIDEO</code><br/>
* <code>android.permission.READ_MEDIA_AUDIO</code><br/>
<br/>
and then add <code>maxSdkVersion="33"</code> to the older permission. See the developer guide for how to do this: <a href="https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions">https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions</a><br/>
<br/>
The <code>MANAGE_EXTERNAL_STORAGE</code> permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at <a href="https://goo.gle/android-mediastore-createwriterequest">https://goo.gle/android-mediastore-createwriterequest</a>.<br/>
<br/>
To learn more, read these resources: Play policy: <a href="https://goo.gle/policy-storage-help">https://goo.gle/policy-storage-help</a> Allowable use cases: <a href="https://goo.gle/policy-storage-usecases">https://goo.gle/policy-storage-usecases</a><br/><div class="moreinfo">More info: <a href="https://goo.gle/android-storage-usecases">https://goo.gle/android-storage-usecases</a>
</div>To suppress this error, use the issue id "ScopedStorage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ScopedStorage</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationScopedStorageLink" onclick="reveal('explanationScopedStorage');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ScopedStorageCardLink" onclick="hideid('ScopedStorageCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DefaultLocale"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DefaultLocaleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied default locale in case conversion</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/utils/DeviceInfoHelper.kt">../../src/main/java/com/remoteandroid/control/utils/DeviceInfoHelper.kt</a>:296</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>String.format(Locale, ...)</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 293 </span>  <span class="keyword">val</span> units = arrayOf(<span class="string">"B"</span>, <span class="string">"KB"</span>, <span class="string">"MB"</span>, <span class="string">"GB"</span>, <span class="string">"TB"</span>)
<span class="lineno"> 294 </span>  <span class="keyword">val</span> digitGroups = (kotlin.math.log10(bytes.toDouble()) / kotlin.math.log10(<span class="number">1024.0</span>)).toInt()
<span class="lineno"> 295 </span>  
<span class="caretline"><span class="lineno"> 296 </span>  <span class="keyword">return</span> <span class="warning">String.format(</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 297 </span>      <span class="string">"%.1f %s"</span>,
<span class="lineno"> 298 </span>      bytes.toDouble() / Math.pow(<span class="number">1024.0</span>, digitGroups.toDouble()),
<span class="lineno"> 299 </span>      units[digitGroups]
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDefaultLocale" style="display: none;">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.ROOT)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DefaultLocale</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDefaultLocaleLink" onclick="reveal('explanationDefaultLocale');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DefaultLocaleCardLink" onclick="hideid('DefaultLocaleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NewApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NewApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Calling new methods on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:240</span>: <span class="message">Call requires API level 26 (current min is 24): <code>android.content.ContextWrapper#startForegroundService</code></span><br /><pre class="errorlines">
<span class="lineno"> 237 </span>                serviceIntent.putExtra(<span class="string">"SCREEN_CAPTURE_RESULT_DATA"</span>, resultData)
<span class="lineno"> 238 </span>            }
<span class="lineno"> 239 </span>
<span class="caretline"><span class="lineno"> 240 </span>            <span class="error">startForegroundService</span>(serviceIntent)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 241 </span>
<span class="lineno"> 242 </span>            isServiceStarted = <span class="keyword">true</span>
<span class="lineno"> 243 </span>            <span class="comment">// 更新状态显示</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNewApi" style="display: none;">
This check scans through all the Android API calls in the application and warns about any calls that are not available on <b>all</b> versions targeted by this application (according to its minimum SDK attribute in the manifest).<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>
<br/>
If you are deliberately setting <code>android:</code> attributes in style definitions, make sure you place this in a <code>values-v</code><i>NN</i> folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.<br/>
<br/>
Similarly, you can use tools:targetApi="11" in an XML file to indicate that the element will only be inflated in an adequate context.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "NewApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NewApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNewApiLink" onclick="reveal('explanationNewApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NewApiCardLink" onclick="hideid('NewApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NotificationPermission"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NotificationPermissionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Notifications Without Permission</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/services/core/RemoteControlForegroundService.kt">../../src/main/java/com/remoteandroid/control/services/core/RemoteControlForegroundService.kt</a>:392</span>: <span class="message">When targeting Android 13 or higher, posting a permission requires holding the <code>POST_NOTIFICATIONS</code> permission</span><br /><pre class="errorlines">
<span class="lineno"> 389 </span>  <span class="keyword">try</span> {
<span class="lineno"> 390 </span>      <span class="keyword">val</span> notification = createNotification(content)
<span class="lineno"> 391 </span>      <span class="keyword">val</span> notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) <span class="keyword">as</span> NotificationManager
<span class="caretline"><span class="lineno"> 392 </span>      <span class="error">notificationManager.notify(NOTIFICATION_ID, notification)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 393 </span>  } catch (e: Exception) {
<span class="lineno"> 394 </span>      Log.e(TAG, <span class="string">"更新通知失败"</span>, e)
<span class="lineno"> 395 </span>  }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNotificationPermission" style="display: none;">
When targeting Android 13 and higher, posting permissions requires holding the runtime permission <code>android.permission.POST_NOTIFICATIONS</code>.<br/>To suppress this error, use the issue id "NotificationPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NotificationPermission</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNotificationPermissionLink" onclick="reveal('explanationNotificationPermission');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NotificationPermissionCardLink" onclick="hideid('NotificationPermissionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="OldTargetApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OldTargetApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Target SDK attribute is not targeting latest version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:13</span>: <span class="message">Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the <code>android.os.Build.VERSION_CODES</code> javadoc for details.</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>    defaultConfig {
<span class="lineno"> 11 </span>        applicationId <span class="string">"com.remoteandroid.control"</span>
<span class="lineno"> 12 </span>        minSdk <span class="number">24</span>
<span class="caretline"><span class="lineno"> 13 </span>        <span class="warning">targetSdk <span class="number">34</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        versionCode <span class="number">1</span>
<span class="lineno"> 15 </span>        versionName <span class="string">"1.0"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOldTargetApi" style="display: none;">
When your application runs on a version of Android that is more recent than your <code>targetSdkVersion</code> specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the <code>targetSdkVersion</code> is less than 14, your app may get an option button in the UI.<br/>
<br/>
To fix this issue, set the <code>targetSdkVersion</code> to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: <a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a> as well as follow this guide:<br/>
<a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a><br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a>
<li><a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a>
</ul></div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "OldTargetApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">OldTargetApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOldTargetApiLink" onclick="reveal('explanationOldTargetApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OldTargetApiCardLink" onclick="hideid('OldTargetApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="BatteryLife"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="BatteryLifeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Battery Life Issues</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt">../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt</a>:190</span>: <span class="message">Use of <code>REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</code> violates the Play Store Content Policy regarding acceptable use cases, as described in <a href="https://developer.android.com/training/monitoring-device-state/doze-standby.html">https://developer.android.com/training/monitoring-device-state/doze-standby.html</a></span><br /><pre class="errorlines">
<span class="lineno"> 187 </span><span class="javadoc">   */</span>
<span class="lineno"> 188 </span>  <span class="keyword">fun</span> requestBatteryOptimizationIgnore(activity: Activity) {
<span class="lineno"> 189 </span>      <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
<span class="caretline"><span class="lineno"> 190 </span>          <span class="keyword">val</span> intent = Intent(Settings.<span class="warning">ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS</span>)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 191 </span>          intent.data = Uri.parse(<span class="string">"package:${</span>context.packageName<span class="string">}"</span>)
<span class="lineno"> 192 </span>          activity.startActivityForResult(intent, PermissionConstants.REQUEST_CODE_BATTERY_OPTIMIZATION)
<span class="lineno"> 193 </span>      }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationBatteryLife" style="display: none;">
This issue flags code that either<br/>
* negatively affects battery life, or<br/>
* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.<br/>
<br/>
Generally, you should be using <code>WorkManager</code> instead.<br/>
<br/>
For more details on how to update your code, please see <a href="https://developer.android.com/topic/performance/background-optimization">https://developer.android.com/topic/performance/background-optimization</a><br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/performance/background-optimization">https://developer.android.com/topic/performance/background-optimization</a>
</div>To suppress this error, use the issue id "BatteryLife" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">BatteryLife</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationBatteryLifeLink" onclick="reveal('explanationBatteryLife');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="BatteryLifeCardLink" onclick="hideid('BatteryLifeCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ProtectedPermissions"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ProtectedPermissionsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using system app permission</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:10</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">  7 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_NETWORK_STATE"</span> />
<span class="lineno">  8 </span>    
<span class="lineno">  9 </span>    <span class="comment">&lt;!-- 无障碍服务权限 --></span>
<span class="caretline"><span class="lineno"> 10 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.BIND_ACCESSIBILITY_SERVICE"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 11 </span>    
<span class="lineno"> 12 </span>    <span class="comment">&lt;!-- 屏幕录制权限 --></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.FOREGROUND_SERVICE"</span> />
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationProtectedPermissions" style="display: none;">
Permissions with the protection level <code>signature</code>, <code>privileged</code> or <code>signatureOrSystem</code> are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions.<br/>To suppress this error, use the issue id "ProtectedPermissions" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ProtectedPermissions</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationProtectedPermissionsLink" onclick="reveal('explanationProtectedPermissions');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ProtectedPermissionsCardLink" onclick="hideid('ProtectedPermissionsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="GradleDependency"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="GradleDependencyCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete Gradle Dependency</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:38</span>: <span class="message">A newer version of androidx.core:core-ktx than 1.12.0 is available: 1.13.1</span><br /><pre class="errorlines">
<span class="lineno"> 35 </span>}
<span class="lineno"> 36 </span>
<span class="lineno"> 37 </span>dependencies {
<span class="caretline"><span class="lineno"> 38 </span>    implementation <span class="warning"><span class="string">'androidx.core:core-ktx:1.12.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 39 </span>    implementation <span class="string">'androidx.appcompat:appcompat:1.6.1'</span>
<span class="lineno"> 40 </span>    implementation <span class="string">'com.google.android.material:material:1.11.0'</span>
<span class="lineno"> 41 </span>    implementation <span class="string">'androidx.constraintlayout:constraintlayout:2.1.4'</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:42</span>: <span class="message">A newer version of androidx.lifecycle:lifecycle-runtime-ktx than 2.7.0 is available: 2.8.3</span><br /><pre class="errorlines">
<span class="lineno"> 39 </span>    implementation <span class="string">'androidx.appcompat:appcompat:1.6.1'</span>
<span class="lineno"> 40 </span>    implementation <span class="string">'com.google.android.material:material:1.11.0'</span>
<span class="lineno"> 41 </span>    implementation <span class="string">'androidx.constraintlayout:constraintlayout:2.1.4'</span>
<span class="caretline"><span class="lineno"> 42 </span>    implementation <span class="warning"><span class="string">'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 43 </span>    implementation <span class="string">'androidx.activity:activity-compose:1.8.2'</span>
<span class="lineno"> 44 </span>    
<span class="lineno"> 45 </span>    <span class="comment">// JSON处理</span></pre>

<span class="location"><a href="../../build.gradle">../../build.gradle</a>:46</span>: <span class="message">A newer version of com.google.code.gson:gson than 2.10.1 is available: 2.11.0</span><br /><pre class="errorlines">
<span class="lineno"> 43 </span>    implementation <span class="string">'androidx.activity:activity-compose:1.8.2'</span>
<span class="lineno"> 44 </span>    
<span class="lineno"> 45 </span>    <span class="comment">// JSON处理</span>
<span class="caretline"><span class="lineno"> 46 </span>    implementation <span class="warning"><span class="string">'com.google.code.gson:gson:2.10.1'</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 47 </span>    
<span class="lineno"> 48 </span>    <span class="comment">// 网络通信</span>
<span class="lineno"> 49 </span>    implementation <span class="string">'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationGradleDependency" style="display: none;">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">GradleDependency</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationGradleDependencyLink" onclick="reveal('explanationGradleDependency');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="GradleDependencyCardLink" onclick="hideid('GradleDependencyCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SwitchIntDef"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SwitchIntDefCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing @IntDef in Switch</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/services/core/RemoteAccessibilityService.kt">../../src/main/java/com/remoteandroid/control/services/core/RemoteAccessibilityService.kt</a>:119</span>: <span class="message">Switch statement on an <code>int</code> with known associated constant missing case <code>AccessibilityEvent.TYPE_ANNOUNCEMENT</code>, <code>AccessibilityEvent.TYPE_ASSIST_READING_CONTEXT</code>, <code>AccessibilityEvent.TYPE_GESTURE_DETECTION_END</code>, <code>AccessibilityEvent.TYPE_GESTURE_DETECTION_START</code>, <code>AccessibilityEvent.TYPE_NOTIFICATION_STATE_CHANGED</code>, <code>AccessibilityEvent.TYPE_SPEECH_STATE_CHANGE</code>, <code>AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_END</code>, <code>AccessibilityEvent.TYPE_TOUCH_EXPLORATION_GESTURE_START</code>, <code>AccessibilityEvent.TYPE_TOUCH_INTERACTION_END</code>, <code>AccessibilityEvent.TYPE_TOUCH_INTERACTION_START</code>, <code>AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED</code>, <code>AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUS_CLEARED</code>, <code>AccessibilityEvent.TYPE_VIEW_CONTEXT_CLICKED</code>, <code>AccessibilityEvent.TYPE_VIEW_FOCUSED</code>, <code>AccessibilityEvent.TYPE_VIEW_HOVER_ENTER</code>, <code>AccessibilityEvent.TYPE_VIEW_HOVER_EXIT</code>, <code>AccessibilityEvent.TYPE_VIEW_LONG_CLICKED</code>, <code>AccessibilityEvent.TYPE_VIEW_SCROLLED</code>, <code>AccessibilityEvent.TYPE_VIEW_SELECTED</code>, <code>AccessibilityEvent.TYPE_VIEW_TARGETED_BY_SCROLL</code>, <code>AccessibilityEvent.TYPE_VIEW_TEXT_SELECTION_CHANGED</code>, <code>AccessibilityEvent.TYPE_VIEW_TEXT_TRAVERSED_AT_MOVEMENT_GRANULARITY</code>, <code>AccessibilityEvent.TYPE_WINDOWS_CHANGED</code>, <code>AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED</code></span><br /><pre class="errorlines">
<span class="lineno"> 116 </span><span class="javadoc">     */</span>
<span class="lineno"> 117 </span>    private <span class="keyword">fun</span> handleAccessibilityEvent(event: AccessibilityEvent) {
<span class="lineno"> 118 </span>        <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 119 </span>            <span class="warning"><span class="keyword">when</span></span> (event.eventType) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 120 </span>                AccessibilityEvent.TYPE_VIEW_CLICKED -> {
<span class="lineno"> 121 </span>                    handleClickEvent(event)
<span class="lineno"> 122 </span>                }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSwitchIntDef" style="display: none;">
This check warns if a <code>switch</code> statement does not explicitly include all the values declared by the typedef <code>@IntDef</code> declaration.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "SwitchIntDef" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SwitchIntDef</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSwitchIntDefLink" onclick="reveal('explanationSwitchIntDef');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SwitchIntDefCardLink" onclick="hideid('SwitchIntDefCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="ObsoleteSdkInt"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ObsoleteSdkIntCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete SDK_INT Version Check</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/constants/AppConstants.kt">../../src/main/java/com/remoteandroid/control/constants/AppConstants.kt</a>:174</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 171 </span><span class="javadoc">     * 检查Android版本兼容性
</span><span class="lineno"> 172 </span><span class="javadoc">     */</span>
<span class="lineno"> 173 </span>    <span class="keyword">fun</span> isAndroidVersionSupported(): Boolean {
<span class="caretline"><span class="lineno"> 174 </span>        <span class="keyword">return</span> <span class="warning">android.os.Build.VERSION.SDK_INT >= MIN_ANDROID_VERSION</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 175 </span>    }
<span class="lineno"> 176 </span>    
<span class="lineno"> 177 </span>    <span class="javadoc">/**
</span></pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/utils/DeviceInfoHelper.kt">../../src/main/java/com/remoteandroid/control/utils/DeviceInfoHelper.kt</a>:140</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 137 </span><span class="javadoc">     */</span>
<span class="lineno"> 138 </span>    private <span class="keyword">fun</span> getCpuAbi(): String {
<span class="lineno"> 139 </span>        <span class="keyword">return</span> <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 140 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 141 </span>                Build.SUPPORTED_ABIS.joinToString(<span class="string">", "</span>)
<span class="lineno"> 142 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 143 </span>                <span class="annotation">@Suppress</span>(<span class="string">"DEPRECATION"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/utils/DeviceInfoHelper.kt">../../src/main/java/com/remoteandroid/control/utils/DeviceInfoHelper.kt</a>:236</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 233 </span>            <span class="string">"apiLevel"</span> to Build.VERSION.SDK_INT,
<span class="lineno"> 234 </span>            <span class="string">"codename"</span> to Build.VERSION.CODENAME,
<span class="lineno"> 235 </span>            <span class="string">"incremental"</span> to Build.VERSION.INCREMENTAL,
<span class="caretline"><span class="lineno"> 236 </span>            <span class="string">"securityPatch"</span> to <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 237 </span>                Build.VERSION.SECURITY_PATCH
<span class="lineno"> 238 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 239 </span>                <span class="string">"Unknown"</span></pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/utils/ImageProcessor.kt">../../src/main/java/com/remoteandroid/control/utils/ImageProcessor.kt</a>:57</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  54 </span><span class="javadoc">   */</span>
<span class="lineno">  55 </span>  <span class="keyword">fun</span> compressBitmapToWebp(bitmap: Bitmap, quality: Int): ByteArray {
<span class="lineno">  56 </span>      <span class="keyword">val</span> outputStream = ByteArrayOutputStream()
<span class="caretline"><span class="lineno">  57 </span>      <span class="keyword">if</span> (<span class="warning">android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.ICE_CREAM_SANDWICH</span>) {</span>
<span class="lineno">  58 </span>          bitmap.compress(Bitmap.CompressFormat.WEBP, quality, outputStream)
<span class="lineno">  59 </span>      } <span class="keyword">else</span> {
<span class="lineno">  60 </span>          <span class="comment">// 降级为JPEG</span></pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/utils/PermissionHelper.kt">../../src/main/java/com/remoteandroid/control/utils/PermissionHelper.kt</a>:134</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 131 </span>       }
<span class="lineno"> 132 </span>       <span class="string">"screen_capture"</span> -> {
<span class="lineno"> 133 </span>           <span class="comment">// 屏幕录制权限需要API 21+</span>
<span class="caretline"><span class="lineno"> 134 </span>           <span class="warning">android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 135 </span>       }
<span class="lineno"> 136 </span>       <span class="string">"overlay"</span> -> {
<span class="lineno"> 137 </span>           <span class="comment">// 悬浮窗权限需要API 23+</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="ObsoleteSdkIntDivLink" onclick="reveal('ObsoleteSdkIntDiv');" />+ 8 More Occurrences...</button>
<div id="ObsoleteSdkIntDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/utils/PermissionHelper.kt">../../src/main/java/com/remoteandroid/control/utils/PermissionHelper.kt</a>:138</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 135 </span>            }
<span class="lineno"> 136 </span>            <span class="string">"overlay"</span> -> {
<span class="lineno"> 137 </span>                <span class="comment">// 悬浮窗权限需要API 23+</span>
<span class="caretline"><span class="lineno"> 138 </span>                <span class="warning">android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 139 </span>            }
<span class="lineno"> 140 </span>            <span class="string">"battery_optimization"</span> -> {
<span class="lineno"> 141 </span>                <span class="comment">// 电池优化需要API 23+</span></pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/utils/PermissionHelper.kt">../../src/main/java/com/remoteandroid/control/utils/PermissionHelper.kt</a>:142</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 139 </span>            }
<span class="lineno"> 140 </span>            <span class="string">"battery_optimization"</span> -> {
<span class="lineno"> 141 </span>                <span class="comment">// 电池优化需要API 23+</span>
<span class="caretline"><span class="lineno"> 142 </span>                <span class="warning">android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 143 </span>            }
<span class="lineno"> 144 </span>            <span class="keyword">else</span> -> <span class="keyword">true</span>
<span class="lineno"> 145 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt">../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt</a>:47</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  44 </span><span class="javadoc">     * 检查悬浮窗权限是否已授予
</span><span class="lineno">  45 </span><span class="javadoc">     */</span>
<span class="lineno">  46 </span>    <span class="keyword">fun</span> isOverlayPermissionGranted(): Boolean {
<span class="caretline"><span class="lineno">  47 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  48 </span>            Settings.canDrawOverlays(context)
<span class="lineno">  49 </span>        } <span class="keyword">else</span> {
<span class="lineno">  50 </span>            <span class="keyword">true</span></pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt">../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt</a>:84</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno">  81 </span><span class="javadoc">     * 检查是否在电池优化白名单中
</span><span class="lineno">  82 </span><span class="javadoc">     */</span>
<span class="lineno">  83 </span>    <span class="keyword">fun</span> isBatteryOptimizationIgnored(): Boolean {
<span class="caretline"><span class="lineno">  84 </span>        <span class="keyword">return</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  85 </span>            <span class="keyword">val</span> powerManager = context.getSystemService(Context.POWER_SERVICE) <span class="keyword">as</span> PowerManager
<span class="lineno">  86 </span>            powerManager.isIgnoringBatteryOptimizations(context.packageName)
<span class="lineno">  87 </span>        } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt">../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt</a>:153</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 150 </span><span class="javadoc">     * 请求悬浮窗权限
</span><span class="lineno"> 151 </span><span class="javadoc">     */</span>
<span class="lineno"> 152 </span>    <span class="keyword">fun</span> requestOverlayPermission(activity: Activity) {
<span class="caretline"><span class="lineno"> 153 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 154 </span>            <span class="keyword">val</span> intent = Intent(
<span class="lineno"> 155 </span>                Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
<span class="lineno"> 156 </span>                Uri.parse(<span class="string">"package:${</span>context.packageName<span class="string">}"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt">../../src/main/java/com/remoteandroid/control/managers/PermissionManager.kt</a>:189</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 186 </span><span class="javadoc">   * 请求电池优化白名单
</span><span class="lineno"> 187 </span><span class="javadoc">   */</span>
<span class="lineno"> 188 </span>  <span class="keyword">fun</span> requestBatteryOptimizationIgnore(activity: Activity) {
<span class="caretline"><span class="lineno"> 189 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 190 </span>          <span class="keyword">val</span> intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
<span class="lineno"> 191 </span>          intent.data = Uri.parse(<span class="string">"package:${</span>context.packageName<span class="string">}"</span>)
<span class="lineno"> 192 </span>          activity.startActivityForResult(intent, PermissionConstants.REQUEST_CODE_BATTERY_OPTIMIZATION)
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/services/core/RemoteControlForegroundService.kt">../../src/main/java/com/remoteandroid/control/services/core/RemoteControlForegroundService.kt</a>:366</span>: <span class="message">Unnecessary; SDK_INT is always >= 24</span><br /><pre class="errorlines">
<span class="lineno"> 363 </span>        
<span class="lineno"> 364 </span>        <span class="keyword">val</span> pendingIntent = PendingIntent.getActivity(
<span class="lineno"> 365 </span>            <span class="keyword">this</span>, <span class="number">0</span>, intent,
<span class="caretline"><span class="lineno"> 366 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.M</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 367 </span>                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
<span class="lineno"> 368 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 369 </span>                PendingIntent.FLAG_UPDATE_CURRENT
</pre>

<span class="location"><a href="../../src/main/res/values/themes.xml">../../src/main/res/values/themes.xml</a>:13</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorSecondaryVariant"</span>>@color/teal_700<span class="tag">&lt;/item></span>
<span class="lineno"> 11 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"colorOnSecondary"</span>>@color/black<span class="tag">&lt;/item></span>
<span class="lineno"> 12 </span>        <span class="comment">&lt;!-- Status bar color. --></span>
<span class="caretline"><span class="lineno"> 13 </span>        <span class="tag">&lt;item</span><span class="attribute"> name</span>=<span class="value">"android:statusBarColor"</span> <span class="warning"><span class="prefix">tools:</span><span class="attribute">targetApi</span>=<span class="value">"l"</span></span>>?attr/colorPrimaryVariant<span class="tag">&lt;/item></span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>        <span class="comment">&lt;!-- Customize your theme here. --></span>
<span class="lineno"> 15 </span>    <span class="tag">&lt;/style></span>
<span class="lineno"> 16 </span><span class="tag">&lt;/resources></span></pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationObsoleteSdkInt" style="display: none;">
This check flags version checks that are not necessary, because the <code>minSdkVersion</code> (or surrounding known API level) is already at least as high as the version checked for.<br/>
<br/>
Similarly, it also looks for resources in <code>-vNN</code> folders, such as <code>values-v14</code> where the version qualifier is less than or equal to the <code>minSdkVersion</code>, where the contents should be merged into the best folder.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteSdkInt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ObsoleteSdkInt</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationObsoleteSdkIntLink" onclick="reveal('explanationObsoleteSdkInt');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ObsoleteSdkIntCardLink" onclick="hideid('ObsoleteSdkIntCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="StaticFieldLeak"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="StaticFieldLeakCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Static Field Leaks</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/services/automation/PasswordRecorder.kt">../../src/main/java/com/remoteandroid/control/services/automation/PasswordRecorder.kt</a>:27</span>: <span class="message">Do not place Android context classes in static fields (static reference to <code>PasswordRecorder</code> which has field <code>context</code> pointing to <code>Context</code>); this is a memory leak</span><br /><pre class="errorlines">
<span class="lineno">  24 </span>        private <span class="keyword">var</span> recordedCoordinates = mutableListOf&lt;PasswordMessage.CoordinatePoint>()
<span class="lineno">  25 </span>        private <span class="keyword">var</span> startTime = <span class="number">0L</span>
<span class="lineno">  26 </span>        
<span class="caretline"><span class="lineno">  27 </span>        <span class="warning"><span class="annotation">@Volatile</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  28 </span>        private <span class="keyword">var</span> instance: PasswordRecorder? = <span class="keyword">null</span>
<span class="lineno">  29 </span>        
<span class="lineno">  30 </span>        <span class="keyword">fun</span> getInstance(context: Context): PasswordRecorder {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationStaticFieldLeak" style="display: none;">
A static field will leak contexts.<br/>
<br/>
Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a <code>Fragment</code> or <code>Activity</code>, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.<br/>
<br/>
Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.<br/>
<br/>
ViewModel classes should never point to Views or non-application Contexts.<br/>To suppress this error, use the issue id "StaticFieldLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">StaticFieldLeak</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationStaticFieldLeakLink" onclick="reveal('explanationStaticFieldLeak');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="StaticFieldLeakCardLink" onclick="hideid('StaticFieldLeakCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="Overdraw"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverdrawCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overdraw: Painting regions more than once</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:9</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/background_primary</code> with a theme that also paints a background (inferred theme is <code>@style/Theme_RemoteAndroidControl</code>)</span><br /><pre class="errorlines">
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="lineno">  7 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>
<span class="lineno">  8 </span>    <span class="prefix">android:</span><span class="attribute">padding</span>=<span class="value">"24dp"</span>
<span class="caretline"><span class="lineno">  9 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/background_primary"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 10 </span>    <span class="prefix">tools:</span><span class="attribute">context</span>=<span class="value">".ui.activities.MainActivity"</span>>
<span class="lineno"> 11 </span>
<span class="lineno"> 12 </span>    <span class="comment">&lt;!-- 顶部标题区域 --></span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:6</span>: <span class="message">Possible overdraw: Root element paints background <code>@color/background_primary</code> with a theme that also paints a background (inferred theme is <code>@style/Theme.RemoteAndroidControl</code>)</span><br /><pre class="errorlines">
<span class="lineno">   3 </span>    <span class="prefix">xmlns:</span><span class="attribute">app</span>=<span class="value">"http://schemas.android.com/apk/res-auto"</span>
<span class="lineno">   4 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   5 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno">   6 </span>    <span class="warning"><span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@color/background_primary"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   7 </span>    <span class="prefix">android:</span><span class="attribute">fillViewport</span>=<span class="value">"true"</span>>
<span class="lineno">   8 </span>
<span class="lineno">   9 </span>    <span class="tag">&lt;LinearLayout</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOverdraw" style="display: none;">
If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called "overdraw".<br/>
<br/>
NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it's currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.<br/>
<br/>
If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.<br/>
<br/>
Of course it's possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead.<br/>To suppress this error, use the issue id "Overdraw" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">Overdraw</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOverdrawLink" onclick="reveal('explanationOverdraw');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverdrawCardLink" onclick="hideid('OverdrawCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedResources"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedResourcesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused resources</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:3</span>: <span class="message">The resource <code>R.color.purple_200</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span><span class="tag">&lt;resources></span>
<span class="caretline"><span class="lineno">  3 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"purple_200"</span></span>>#FFBB86FC<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_500"</span>>#FF6200EE<span class="tag">&lt;/color></span>
<span class="lineno">  5 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"purple_700"</span>>#FF3700B3<span class="tag">&lt;/color></span>
<span class="lineno">  6 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"teal_200"</span>>#FF03DAC5<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:12</span>: <span class="message">The resource <code>R.color.colorPrimary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno">  9 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"white"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 10 </span>
<span class="lineno"> 11 </span>    <span class="comment">&lt;!-- 主题颜色 --></span>
<span class="caretline"><span class="lineno"> 12 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"colorPrimary"</span></span>>#FF2196F3<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorPrimaryDark"</span>>#FF1976D2<span class="tag">&lt;/color></span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorAccent"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:13</span>: <span class="message">The resource <code>R.color.colorPrimaryDark</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 10 </span>
<span class="lineno"> 11 </span>    <span class="comment">&lt;!-- 主题颜色 --></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorPrimary"</span>>#FF2196F3<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"colorPrimaryDark"</span></span>>#FF1976D2<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 14 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorAccent"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno"> 15 </span>
<span class="lineno"> 16 </span>    <span class="comment">&lt;!-- 背景颜色 --></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:14</span>: <span class="message">The resource <code>R.color.colorAccent</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span>    <span class="comment">&lt;!-- 主题颜色 --></span>
<span class="lineno"> 12 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorPrimary"</span>>#FF2196F3<span class="tag">&lt;/color></span>
<span class="lineno"> 13 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorPrimaryDark"</span>>#FF1976D2<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 14 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"colorAccent"</span></span>>#FF03DAC5<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span>
<span class="lineno"> 16 </span>    <span class="comment">&lt;!-- 背景颜色 --></span>
<span class="lineno"> 17 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"backgroundColor"</span>>#FFF5F5F5<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:17</span>: <span class="message">The resource <code>R.color.backgroundColor</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 14 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"colorAccent"</span>>#FF03DAC5<span class="tag">&lt;/color></span>
<span class="lineno"> 15 </span>
<span class="lineno"> 16 </span>    <span class="comment">&lt;!-- 背景颜色 --></span>
<span class="caretline"><span class="lineno"> 17 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"backgroundColor"</span></span>>#FFF5F5F5<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"cardBackgroundColor"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"background_primary"</span>>#FFF8F9FA<span class="tag">&lt;/color></span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"card_background"</span>>#FFFFFFFF<span class="tag">&lt;/color></span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="UnusedResourcesDivLink" onclick="reveal('UnusedResourcesDiv');" />+ 4 More Occurrences...</button>
<div id="UnusedResourcesDiv" style="display: none">
<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:18</span>: <span class="message">The resource <code>R.color.cardBackgroundColor</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>
<span class="lineno"> 16 </span>    <span class="comment">&lt;!-- 背景颜色 --></span>
<span class="lineno"> 17 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"backgroundColor"</span>>#FFF5F5F5<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 18 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"cardBackgroundColor"</span></span>>#FFFFFFFF<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"background_primary"</span>>#FFF8F9FA<span class="tag">&lt;/color></span>
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"card_background"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:23</span>: <span class="message">The resource <code>R.color.textColorPrimary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 20 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"card_background"</span>>#FFFFFFFF<span class="tag">&lt;/color></span>
<span class="lineno"> 21 </span>
<span class="lineno"> 22 </span>    <span class="comment">&lt;!-- 文本颜色 --></span>
<span class="caretline"><span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"textColorPrimary"</span></span>>#FF212121<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textColorSecondary"</span>>#FF757575<span class="tag">&lt;/color></span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_primary"</span>>#FF212121<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_secondary"</span>>#FF757575<span class="tag">&lt;/color></span></pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:24</span>: <span class="message">The resource <code>R.color.textColorSecondary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>
<span class="lineno"> 22 </span>    <span class="comment">&lt;!-- 文本颜色 --></span>
<span class="lineno"> 23 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"textColorPrimary"</span>>#FF212121<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 24 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"textColorSecondary"</span></span>>#FF757575<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_primary"</span>>#FF212121<span class="tag">&lt;/color></span>
<span class="lineno"> 26 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"text_secondary"</span>>#FF757575<span class="tag">&lt;/color></span>
</pre>

<span class="location"><a href="../../src/main/res/values/colors.xml">../../src/main/res/values/colors.xml</a>:30</span>: <span class="message">The resource <code>R.color.accent_secondary</code> appears to be unused</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span>
<span class="lineno"> 28 </span>    <span class="comment">&lt;!-- 强调色 --></span>
<span class="lineno"> 29 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"accent_primary"</span>>#FF2196F3<span class="tag">&lt;/color></span>
<span class="caretline"><span class="lineno"> 30 </span>    <span class="tag">&lt;color</span><span class="attribute"> </span><span class="warning"><span class="attribute">name</span>=<span class="value">"accent_secondary"</span></span>>#FF03DAC5<span class="tag">&lt;/color></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>
<span class="lineno"> 32 </span>    <span class="comment">&lt;!-- 按钮颜色 --></span>
<span class="lineno"> 33 </span>    <span class="tag">&lt;color</span><span class="attribute"> name</span>=<span class="value">"button_primary"</span>>#FF2196F3<span class="tag">&lt;/color></span></pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationUnusedResources" style="display: none;">
Unused resources make applications larger and slow down builds.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
,<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedResources</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedResourcesLink" onclick="reveal('explanationUnusedResources');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedResourcesCardLink" onclick="hideid('UnusedResourcesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability"></a>
<a name="ButtonStyle"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ButtonStyleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Button should be borderless</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:159</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 156 </span>                <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>>
<span class="lineno"> 157 </span>
<span class="lineno"> 158 </span>                <span class="comment">&lt;!-- 跳过按钮 --></span>
<span class="caretline"><span class="lineno"> 159 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 160 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_skip"</span>
<span class="lineno"> 161 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 162 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"48dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:171</span>: <span class="message">Buttons in button bars should be borderless; use <code>style="?android:attr/buttonBarButtonStyle"</code> (and <code>?android:attr/buttonBarStyle</code> on the parent)</span><br /><pre class="errorlines">
<span class="lineno"> 168 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginEnd</span>=<span class="value">"8dp"</span> />
<span class="lineno"> 169 </span>
<span class="lineno"> 170 </span>                <span class="comment">&lt;!-- 下一步按钮 --></span>
<span class="caretline"><span class="lineno"> 171 </span>                <span class="tag">&lt;</span><span class="warning"><span class="tag">Button</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 172 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_next"</span>
<span class="lineno"> 173 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 174 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"48dp"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationButtonStyle" style="display: none;">
Button bars typically use a borderless style for the buttons. Set the <code>style="?android:attr/buttonBarButtonStyle"</code> attribute on each of the buttons, and set <code>style="?android:attr/buttonBarStyle"</code> on the parent layout<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/dialogs">https://d.android.com/r/studio-ui/designer/material/dialogs</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ButtonStyle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ButtonStyle</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationButtonStyleLink" onclick="reveal('explanationButtonStyle');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ButtonStyleCardLink" onclick="hideid('ButtonStyleCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Accessibility"></a>
<a name="ContentDescription"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ContentDescriptionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image without contentDescription</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:20</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 17 </span>        <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno"> 18 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"32dp"</span>>
<span class="lineno"> 19 </span>
<span class="caretline"><span class="lineno"> 20 </span>        <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 21 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"80dp"</span>
<span class="lineno"> 22 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"80dp"</span>
<span class="lineno"> 23 </span>            <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@mipmap/ic_launcher"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:23</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  20 </span>            <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"center"</span>
<span class="lineno">  21 </span>            <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"32dp"</span>>
<span class="lineno">  22 </span>
<span class="caretline"><span class="lineno">  23 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  24 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"80dp"</span>
<span class="lineno">  25 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"80dp"</span>
<span class="lineno">  26 </span>                <span class="prefix">android:</span><span class="attribute">src</span>=<span class="value">"@mipmap/ic_launcher"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationContentDescription" style="display: none;">
Non-textual widgets like ImageViews and ImageButtons should use the <code>contentDescription</code> attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.<br/>
<br/>
Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to <code>@null</code>. If your app's minSdkVersion is 16 or higher, you can instead set these graphical elements' <code>android:importantForAccessibility</code> attributes to <code>no</code>.<br/>
<br/>
Note that for text fields, you should not set both the <code>hint</code> and the <code>contentDescription</code> attributes since the hint will never be shown. Just set the <code>hint</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases">https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ContentDescription" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ContentDescription</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationContentDescriptionLink" onclick="reveal('explanationContentDescription');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ContentDescriptionCardLink" onclick="hideid('ContentDescriptionCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="SetTextI18n"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SetTextI18nCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">TextView Internationalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:58</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno">  55 </span>            Log.d(<span class="string">"MainActivity"</span>, <span class="string">"服务通知已发送"</span>)
<span class="lineno">  56 </span>
<span class="lineno">  57 </span>            <span class="comment">// 更新UI状态</span>
<span class="caretline"><span class="lineno">  58 </span>            statusText.text = <span class="warning"><span class="string">"✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 正在启动屏幕录制..."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  59 </span>
<span class="lineno">  60 </span>        } <span class="keyword">else</span> {
<span class="lineno">  61 </span>            Log.w(<span class="string">"MainActivity"</span>, <span class="string">"❌ 屏幕录制权限被拒绝，resultCode: ${</span>result.resultCode<span class="string">}"</span>)
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:62</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno">  59 </span>
<span class="lineno">  60 </span>        } <span class="keyword">else</span> {
<span class="lineno">  61 </span>            Log.w(<span class="string">"MainActivity"</span>, <span class="string">"❌ 屏幕录制权限被拒绝，resultCode: ${</span>result.resultCode<span class="string">}"</span>)
<span class="caretline"><span class="lineno">  62 </span>            statusText.text = <span class="warning"><span class="string">"✅ 基础权限已授予\n❌ 屏幕录制权限被拒绝\n请重新申请权限以启用完整功能"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  63 </span>
<span class="lineno">  64 </span>            <span class="comment">// 通知服务屏幕录制权限被拒绝</span>
<span class="lineno">  65 </span>            notifyServiceScreenCapturePermissionDenied()
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:144</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 141 </span>  <span class="comment">// 基础权限已授予，立即启动服务</span>
<span class="lineno"> 142 </span>  <span class="keyword">if</span> (!isServiceStarted) {
<span class="lineno"> 143 </span>      startRemoteControlService() <span class="comment">// 先启动服务，不需要屏幕录制权限</span>
<span class="caretline"><span class="lineno"> 144 </span>      statusText.text = <span class="warning"><span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span></span></span>
<span class="lineno"> 145 </span>      permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 146 </span>  } <span class="keyword">else</span> {
<span class="lineno"> 147 </span>      statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span></pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:144</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 141 </span>  <span class="comment">// 基础权限已授予，立即启动服务</span>
<span class="lineno"> 142 </span>  <span class="keyword">if</span> (!isServiceStarted) {
<span class="lineno"> 143 </span>      startRemoteControlService() <span class="comment">// 先启动服务，不需要屏幕录制权限</span>
<span class="caretline"><span class="lineno"> 144 </span>      statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n</span><span class="warning"><span class="string">📱 等待PC端连接...</span></span><span class="string">\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span></span>
<span class="lineno"> 145 </span>      permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 146 </span>  } <span class="keyword">else</span> {
<span class="lineno"> 147 </span>      statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span></pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:144</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 141 </span>  <span class="comment">// 基础权限已授予，立即启动服务</span>
<span class="lineno"> 142 </span>  <span class="keyword">if</span> (!isServiceStarted) {
<span class="lineno"> 143 </span>      startRemoteControlService() <span class="comment">// 先启动服务，不需要屏幕录制权限</span>
<span class="caretline"><span class="lineno"> 144 </span>      statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n</span><span class="warning"><span class="string">💡 PC端点击'开始录制'将申请屏幕录制权限</span></span><span class="string">"</span></span>
<span class="lineno"> 145 </span>      permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 146 </span>  } <span class="keyword">else</span> {
<span class="lineno"> 147 </span>      statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="SetTextI18nDivLink" onclick="reveal('SetTextI18nDiv');" />+ 10 More Occurrences...</button>
<div id="SetTextI18nDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:147</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 144 </span>          statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span>
<span class="lineno"> 145 </span>          permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 146 </span>      } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 147 </span>          statusText.text = <span class="warning"><span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span></span></span>
<span class="lineno"> 148 </span>          permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 149 </span>      }
<span class="lineno"> 150 </span>  } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:147</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 144 </span>          statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span>
<span class="lineno"> 145 </span>          permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 146 </span>      } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 147 </span>          statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n</span><span class="warning"><span class="string">📱 等待PC端连接...</span></span><span class="string">\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span></span>
<span class="lineno"> 148 </span>          permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 149 </span>      }
<span class="lineno"> 150 </span>  } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:147</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 144 </span>          statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n💡 PC端点击'开始录制'将申请屏幕录制权限"</span>
<span class="lineno"> 145 </span>          permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 146 </span>      } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 147 </span>          statusText.text = <span class="string">"✅ 基础权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接...\n\n</span><span class="warning"><span class="string">💡 PC端点击'开始录制'将申请屏幕录制权限</span></span><span class="string">"</span></span>
<span class="lineno"> 148 </span>          permissionButton.text = <span class="string">"手动申请屏幕录制权限"</span>
<span class="lineno"> 149 </span>      }
<span class="lineno"> 150 </span>  } <span class="keyword">else</span> {
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:211</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 208 </span>            Log.d(<span class="string">"MainActivity"</span>, <span class="string">"权限申请Intent已启动"</span>)
<span class="lineno"> 209 </span>
<span class="lineno"> 210 </span>            <span class="comment">// 更新UI状态</span>
<span class="caretline"><span class="lineno"> 211 </span>            statusText.text = <span class="warning"><span class="string">"✅ 基础权限已授予\n🔄 正在申请屏幕录制权限...\n请在弹出的对话框中点击'立即开始'"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 212 </span>
<span class="lineno"> 213 </span>        } catch (e: Exception) {
<span class="lineno"> 214 </span>            Log.e(<span class="string">"MainActivity"</span>, <span class="string">"启动权限申请失败"</span>, e)
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:216</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 213 </span>        } catch (e: Exception) {
<span class="lineno"> 214 </span>            Log.e(<span class="string">"MainActivity"</span>, <span class="string">"启动权限申请失败"</span>, e)
<span class="lineno"> 215 </span>            isScreenCapturePermissionRequested = <span class="keyword">false</span>
<span class="caretline"><span class="lineno"> 216 </span>            statusText.text = <span class="warning"><span class="string">"✅ 基础权限已授予\n❌ 屏幕录制权限申请失败: ${</span>e.message<span class="string">}"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 217 </span>        }
<span class="lineno"> 218 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:244</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 241 </span>
<span class="lineno"> 242 </span>            isServiceStarted = <span class="keyword">true</span>
<span class="lineno"> 243 </span>            <span class="comment">// 更新状态显示</span>
<span class="caretline"><span class="lineno"> 244 </span>            statusText.text = <span class="warning"><span class="string">"✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接..."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 245 </span>        } catch (e: Exception) {
<span class="lineno"> 246 </span>            statusText.text = <span class="string">"✅ 所有权限已授予\n❌ 服务启动失败: ${</span>e.message<span class="string">}"</span>
<span class="lineno"> 247 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:244</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 241 </span>
<span class="lineno"> 242 </span>            isServiceStarted = <span class="keyword">true</span>
<span class="lineno"> 243 </span>            <span class="comment">// 更新状态显示</span>
<span class="caretline"><span class="lineno"> 244 </span>            statusText.text = <span class="string">"✅ 所有权限已授予\n🚀 远程控制服务已启动\n</span><span class="warning"><span class="string">📱 等待PC端连接...</span></span><span class="string">"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 245 </span>        } catch (e: Exception) {
<span class="lineno"> 246 </span>            statusText.text = <span class="string">"✅ 所有权限已授予\n❌ 服务启动失败: ${</span>e.message<span class="string">}"</span>
<span class="lineno"> 247 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:246</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 243 </span>            <span class="comment">// 更新状态显示</span>
<span class="lineno"> 244 </span>            statusText.text = <span class="string">"✅ 所有权限已授予\n🚀 远程控制服务已启动\n📱 等待PC端连接..."</span>
<span class="lineno"> 245 </span>        } catch (e: Exception) {
<span class="caretline"><span class="lineno"> 246 </span>            statusText.text = <span class="warning"><span class="string">"✅ 所有权限已授予\n❌ 服务启动失败: ${</span>e.message<span class="string">}"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 247 </span>        }
<span class="lineno"> 248 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:261</span>: <span class="message">Do not concatenate text displayed with <code>setText</code>. Use resource string with placeholders.</span><br /><pre class="errorlines">
<span class="lineno"> 258 </span>
<span class="lineno"> 259 </span>            <span class="comment">// 直接申请屏幕录制权限</span>
<span class="lineno"> 260 </span>            <span class="keyword">if</span> (!isScreenCapturePermissionRequested) {
<span class="caretline"><span class="lineno"> 261 </span>                statusText.text = <span class="warning"><span class="string">"🎯 PC端请求屏幕录制权限\n正在申请屏幕录制权限..."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 262 </span>                requestScreenCapturePermission()
<span class="lineno"> 263 </span>            }
<span class="lineno"> 264 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt">../../src/main/java/com/remoteandroid/control/ui/activities/MainActivity.kt</a>:261</span>: <span class="message">String literal in <code>setText</code> can not be translated. Use Android resources instead.</span><br /><pre class="errorlines">
<span class="lineno"> 258 </span>
<span class="lineno"> 259 </span>            <span class="comment">// 直接申请屏幕录制权限</span>
<span class="lineno"> 260 </span>            <span class="keyword">if</span> (!isScreenCapturePermissionRequested) {
<span class="caretline"><span class="lineno"> 261 </span>                statusText.text = <span class="string">"</span><span class="warning"><span class="string">🎯 PC端请求屏幕录制权限</span></span><span class="string">\n正在申请屏幕录制权限..."</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 262 </span>                requestScreenCapturePermission()
<span class="lineno"> 263 </span>            }
<span class="lineno"> 264 </span>        }
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationSetTextI18n" style="display: none;">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SetTextI18n</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSetTextI18nLink" onclick="reveal('explanationSetTextI18n');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SetTextI18nCardLink" onclick="hideid('SetTextI18nCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:30</span>: <span class="message">Hardcoded string "RemoteAndroid Control", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 27 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_title"</span>
<span class="lineno"> 28 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 29 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 30 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"RemoteAndroid Control"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 31 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"24sp"</span>
<span class="lineno"> 32 </span>            <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno"> 33 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:39</span>: <span class="message">Hardcoded string "Android&#36828;&#31243;&#25511;&#21046;&#23458;&#25143;&#31471;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 37 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 38 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 39 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Android远程控制客户端"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 40 </span>            <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno"> 41 </span>            <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:64</span>: <span class="message">Hardcoded string "&#27491;&#22312;&#21021;&#22987;&#21270;...", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 61 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_status"</span>
<span class="lineno"> 62 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 63 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 64 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"正在初始化..."</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 65 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno"> 66 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno"> 67 </span>                <span class="prefix">android:</span><span class="attribute">lineSpacingExtra</span>=<span class="value">"4dp"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_main.xml">../../src/main/res/layout/activity_main.xml</a>:79</span>: <span class="message">Hardcoded string "&#30003;&#35831;&#26435;&#38480;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 76 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_permission_guide"</span>
<span class="lineno"> 77 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 78 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span>
<span class="caretline"><span class="lineno"> 79 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"申请权限"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 80 </span>        <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno"> 81 </span>        <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno"> 82 </span>        <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:32</span>: <span class="message">Hardcoded string "RemoteAndroid Control", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  29 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  30 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  31 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  32 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"RemoteAndroid Control"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  33 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"24sp"</span>
<span class="lineno">  34 </span>                <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  35 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span></pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="HardcodedTextDivLink" onclick="reveal('HardcodedTextDiv');" />+ 8 More Occurrences...</button>
<div id="HardcodedTextDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:41</span>: <span class="message">Hardcoded string "&#26435;&#38480;&#30003;&#35831;&#21521;&#23548;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  38 </span>            <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno">  39 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  40 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  41 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"权限申请向导"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  42 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno">  43 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:67</span>: <span class="message">Hardcoded string "&#26080;&#38556;&#30861;&#26435;&#38480;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  64 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_permission_title"</span>
<span class="lineno">  65 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  66 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  67 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"无障碍权限"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  68 </span>                    <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"20sp"</span>
<span class="lineno">  69 </span>                    <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno">  70 </span>                    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/accent_primary"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:78</span>: <span class="message">Hardcoded string "&#29992;&#20110;&#23454;&#29616;&#23494;&#30721;&#35760;&#24405;&#21644;&#33258;&#21160;&#21270;&#25805;&#20316;&#21151;&#33021;&#65292;&#36825;&#26159;&#24212;&#29992;&#30340;&#26680;&#24515;&#21151;&#33021;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  75 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_permission_description"</span>
<span class="lineno">  76 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  77 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  78 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"用于实现密码记录和自动化操作功能，这是应用的核心功能"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  79 </span>                    <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno">  80 </span>                    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span>
<span class="lineno">  81 </span>                    <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"16dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:88</span>: <span class="message">Hardcoded string "RemoteAndroid&#38656;&#35201;&#26080;&#38556;&#30861;&#26435;&#38480;&#26469;&#30417;&#21548;&#23631;&#24149;&#19978;&#30340;&#28857;&#20987;&#20107;&#20214;&#65292;&#23454;&#29616;&#23494;&#30721;&#35760;&#24405;&#21151;&#33021;&#12290;&#27492;&#26435;&#38480;&#20165;&#29992;&#20110;&#35760;&#24405;&#24744;&#30340;&#25805;&#20316;&#65292;&#19981;&#20250;&#25910;&#38598;&#20219;&#20309;&#20010;&#20154;&#38544;&#31169;&#20449;&#24687;&#12290;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno">  85 </span><span class="attribute">  </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/tv_permission_reason"</span>
<span class="lineno">  86 </span>  <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  87 </span>  <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno">  88 </span>  <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"RemoteAndroid需要无障碍权限来监听屏幕上的点击事件，实现密码记录功能。此权限仅用于记录您的操作，不会收集任何个人隐私信息。"</span></span></span>
<span class="lineno">  89 </span>  <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno">  90 </span>  <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span>
<span class="lineno">  91 </span>  <span class="prefix">android:</span><span class="attribute">lineSpacingExtra</span>=<span class="value">"4dp"</span> />
</pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:115</span>: <span class="message">Hardcoded string "&#30003;&#35831;&#27493;&#39588;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 112 </span>                <span class="tag">&lt;TextView</span><span class="attribute">
</span><span class="lineno"> 113 </span><span class="attribute">                    </span><span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 114 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 115 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"申请步骤"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 116 </span>                    <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"18sp"</span>
<span class="lineno"> 117 </span>                    <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno"> 118 </span>                    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_primary"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:144</span>: <span class="message">Hardcoded string "&#30003;&#35831;&#26435;&#38480;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 141 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_request_permission"</span>
<span class="lineno"> 142 </span>                <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 143 </span>                <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"56dp"</span>
<span class="caretline"><span class="lineno"> 144 </span>                <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"申请权限"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 145 </span>                <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"16sp"</span>
<span class="lineno"> 146 </span>                <span class="prefix">android:</span><span class="attribute">textStyle</span>=<span class="value">"bold"</span>
<span class="lineno"> 147 </span>                <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/white"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:164</span>: <span class="message">Hardcoded string "&#36339;&#36807;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 161 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 162 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"48dp"</span>
<span class="lineno"> 163 </span>                    <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 164 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"跳过"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 165 </span>                    <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno"> 166 </span>                    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/text_secondary"</span>
<span class="lineno"> 167 </span>                    <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/button_secondary"</span></pre>

<span class="location"><a href="../../src/main/res/layout/activity_permission_guide.xml">../../src/main/res/layout/activity_permission_guide.xml</a>:176</span>: <span class="message">Hardcoded string "&#19979;&#19968;&#27493;", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 173 </span>                    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span>
<span class="lineno"> 174 </span>                    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"48dp"</span>
<span class="lineno"> 175 </span>                    <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 176 </span>                    <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"下一步"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 177 </span>                    <span class="prefix">android:</span><span class="attribute">textSize</span>=<span class="value">"14sp"</span>
<span class="lineno"> 178 </span>                    <span class="prefix">android:</span><span class="attribute">textColor</span>=<span class="value">"@color/accent_primary"</span>
<span class="lineno"> 179 </span>                    <span class="prefix">android:</span><span class="attribute">background</span>=<span class="value">"@drawable/button_outline"</span></pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="ExtraIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ExtraIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Included Additional Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
This card lists all the extra checks run by lint, provided from libraries,
build configuration and extra flags. This is included to help you verify
whether a particular check is included in analysis when configuring builds.
(Note that the list does not include the hundreds of built-in checks into lint,
only additional ones.)
<div id="IncludedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">ComposableModifierFactory<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions that need to be aware of the composition should use androidx.compose.ui.composed {} in their implementation instead of being marked as @Composable. This allows Modifiers to be referenced in top level variables and constructed outside of the composition.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
@Composable functions without a return type should use similar naming to classes, starting with an uppercase letter and ending with a noun. @Composable functions with a return type should be treated as normal Kotlin functions, starting with a lowercase letter.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CompositionLocalNaming<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
CompositionLocal properties should be prefixed with <code>Local</code>. This helps make it clear at their use site that these values are local to the current composition. Typically the full name will be <code>Local</code> + the type of the CompositionLocal, for example val LocalFoo = compositionLocalOf { Foo() }.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CoroutineCreationDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a coroutine with <code>async</code> or <code>launch</code> during composition is often incorrect - this means that a coroutine will be created even if the composition fails / is rolled back, and it also means that multiple coroutines could end up mutating the same state, causing inconsistent results. Instead, use <code>LaunchedEffect</code> and create coroutines inside the suspending block. The block will only run after a successful composition, and will cancel existing coroutines when <code>key</code> changes, allowing correct cleanup.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerMetadata<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When a library defines a Initializer, it needs to be accompanied by a corresponding &lt;meta-data> entry in the AndroidManifest.xml file.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnsureInitializerNoArgConstr<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Every <code>Initializer</code> must have a no argument constructor.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.startup<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=823348">https://issuetracker.google.com/issues/new?component=823348</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExperimentalAnnotationRetention<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Experimental annotations defined in Java source should use default (<code>CLASS</code>) retention, while Kotlin-sourced annotations should use <code>BINARY</code> retention.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentBackPressedCallback<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The Fragment lifecycle can result in a Fragment being active                 longer than its view. This can lead to unexpected behavior from lifecycle aware                 objects remaining active longer than the Fragment's view. To solve this issue,                 getViewLifecycleOwner() should be used as a LifecycleOwner rather than the                 Fragment instance once it is safe to access the view lifecycle in a                 Fragment's onCreateView, onViewCreated, onActivityCreated, or                 onViewStateRestored methods.<br/><div class="vendor">
Vendor: Android Open Source Project (androidx.fragment.lint.fragment)<br/>
Identifier: androidx.fragment.lint.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=192731">https://issuetracker.google.com/issues/new?component=192731</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentLiveDataObserve<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
When observing a LiveData object from a fragment's onCreateView,                 onViewCreated, onActivityCreated, or onViewStateRestored method                 getViewLifecycleOwner() should be used as the LifecycleOwner rather than the                 Fragment instance. The Fragment lifecycle can result in the Fragment being                 active longer than its view. This can lead to unexpected behavior from                 LiveData objects being observed longer than the Fragment's view is active.<br/><div class="vendor">
Vendor: Android Open Source Project (androidx.fragment.lint.fragment)<br/>
Identifier: androidx.fragment.lint.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=192731">https://issuetracker.google.com/issues/new?component=192731</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FragmentTagUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
FragmentContainerView replaces the &lt;fragment> tag as the preferred                 way of adding fragments via XML. Unlike the &lt;fragment> tag, FragmentContainerView                 uses a normal <code>FragmentTransaction</code> under the hood to add the initial fragment,                 allowing further FragmentTransaction operations on the FragmentContainerView                 and providing a consistent timing for lifecycle events.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html">https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html</a>
</div><div class="vendor">
Vendor: Android Open Source Project (androidx.fragment.lint.fragment)<br/>
Identifier: androidx.fragment.lint.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=192731">https://issuetracker.google.com/issues/new?component=192731</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidColorHexValue<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined / incorrect.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidFragmentVersionForActivityResult<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
In order to use the ActivityResult APIs you must upgrade your                 Fragment version to 1.3.0. Previous versions of FragmentActivity                 failed to call super.onRequestPermissionsResult() and used invalid request codes<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/permissions/requesting#make-the-request">https://developer.android.com/training/permissions/requesting#make-the-request</a>
</div><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.activity<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=527362">https://issuetracker.google.com/issues/new?component=527362</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LaunchDuringComposition<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Calling <code>launch</code> during composition is incorrect. Doing so will cause launch to be called multiple times resulting in a RuntimeException. Instead, use <code>SideEffect</code> and <code>launch</code> inside of the suspending block. The block will only run after a successful composition.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingColorAlphaChannel<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Creating a Color with a hex value requires a 32 bit value (such as 0xFF000000), with 8 bits being used per channel (ARGB). Not passing a full 32 bit value will result in channels being undefined. For example, passing 0xFF0000 will result in a missing alpha channel, so the color will not appear visible.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui.graphics<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryExtensionFunction<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should be defined as extension functions on Modifier to allow modifiers to be fluently chained.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions should return Modifier as their type, and not a subtype of Modifier (such as Modifier.Element).<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierFactoryUnreferencedReceiver<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Modifier factory functions are fluently chained to construct a chain of Modifier objects that will be applied to a layout. As a result, each factory function <i>must</i> use the receiver <code>Modifier</code> parameter, to ensure that the function is returning a chain that includes previous items in the chain. Make sure the returned chain either explicitly includes <code>this</code>, such as <code>return this.then(MyModifier)</code> or implicitly by returning a chain that starts with an implicit call to another factory function, such as <code>return myModifier()</code>, where <code>myModifier</code> is defined as <code>fun Modifier.myModifier(): Modifier</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ModifierParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first (or only) Modifier parameter in a Composable function should follow the following rules:<br/>
- Be named <code>modifier</code><br/>
- Have a type of <code>Modifier</code><br/>
- Either have no default value, or have a default value of <code>Modifier</code><br/>
- If optional, be the first optional parameter in the parameter list<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoCollectCallFound<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
You must call collect on the progress in the onBack function. The collect call is what properly splits the callback so it knows what to do when the back gestures is started vs when it is completed. Failing to call collect will cause all code in the block to run when the gesture is started.<br/><div class="vendor">
Vendor: Jetpack Activity Compose<br/>
Identifier: androidx.activity.compose<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NullSafeMutableLiveData<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This check ensures that LiveData values are not null when explicitly                 declared as non-nullable.<br/>
<br/>
                Kotlin interoperability does not support enforcing explicit null-safety when using                 generic Java type parameters. Since LiveData is a Java class its value can always                 be null even when its type is explicitly declared as non-nullable. This can lead                 to runtime exceptions from reading a null LiveData value that is assumed to be                 non-nullable.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberReturnType<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
A call to <code>remember</code> that returns <code>Unit</code> is always an error. This typically happens when using <code>remember</code> to mutate variables on an object. <code>remember</code> is executed during the composition, which means that if the composition fails or is happening on a separate thread, the mutated variables may not reflect the true state of the composition. Instead, use <code>SideEffect</code> to make deferred changes once the composition succeeds, or mutate <code>MutableState</code> backed variables directly, as these will handle composition failure for you.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RememberSaveableSaverParameter<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The first parameter to <code>rememberSaveable</code> is a vararg parameter for inputs that when changed will cause the state to reset. Passing a <code>Saver</code> object to this parameter is an error, as the intention is to pass the <code>Saver</code> object to the saver parameter. Since the saver parameter is not the first parameter, it must be explicitly named.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime.saveable<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RepeatOnLifecycleWrongUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
The repeatOnLifecycle APIs should be used when the View is created,                 that is in the <code>onCreate</code> lifecycle method for Activities, or <code>onViewCreated</code> in                 case you're using Fragments.<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnnecessaryComposedModifier<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>Modifier.composed</code> allows invoking @Composable functions when creating a <code>Modifier</code> instance - for example, using <code>remember</code> to have instance-specific state, allowing the same <code>Modifier</code> object to be safely used in multiple places. Using <code>Modifier.composed</code> without calling any @Composable functions inside is unnecessary, and since the Modifier is no longer skippable, this can cause a lot of extra work inside the composed body, leading to worse performance.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.ui<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnrememberedMutableState<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
State objects created during composition need to be `remember`ed, otherwise they will be recreated during recomposition, and lose their state. Either hoist the state to an object that is not created during composition, or wrap the state in a call to <code>remember</code>.<br/><div class="vendor">
Vendor: Jetpack Compose<br/>
Identifier: androidx.compose.runtime<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=612128">https://issuetracker.google.com/issues/new?component=612128</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeLifecycleWhenUsage<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
If the <code>Lifecycle</code> is destroyed within the block of                     <code>Lifecycle.whenStarted</code> or any similar <code>Lifecycle.when</code> method is suspended,                     the block will be cancelled, which will also cancel any child coroutine                     launched inside the block. As as a result, If you have a try finally block                     in your code, the finally might run after the Lifecycle moves outside                     the desired state. It is recommended to check the <code>Lifecycle.isAtLeast</code>                     before accessing UI in finally block. Similarly,                     if you have a catch statement that might catch <code>CancellationException</code>,                     you should check the <code>Lifecycle.isAtLeast</code> before accessing the UI. See                     documentation of <code>Lifecycle.whenStateAtLeast</code> for more details<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.lifecycle<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=413132">https://issuetracker.google.com/issues/new?component=413132</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageError<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with error-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageError</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageError">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeOptInUsageWarning<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
This API has been flagged as opt-in with warning-level severity.<br/>
<br/>
Any declaration annotated with this marker is considered part of an unstable or<br/>
otherwise non-standard API surface and its call sites should accept the opt-in<br/>
aspect of it by using the <code>@OptIn</code> annotation, using the marker annotation --<br/>
effectively causing further propagation of the opt-in aspect -- or configuring<br/>
the <code>UnsafeOptInUsageWarning</code> check's options for project-wide opt-in.<br/>
<br/>
To configure project-wide opt-in, specify the <code>opt-in</code> option value in <code>lint.xml</code><br/>
as a comma-delimited list of opted-in annotations:<br/>

<pre>
&lt;lint>
    &lt;issue id="UnsafeOptInUsageWarning">
        &lt;option name="opt-in" value="com.foo.ExperimentalBarAnnotation" />
    &lt;/issue>
&lt;/lint>
</pre>
<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.annotation.experimental<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=459778">https://issuetracker.google.com/issues/new?component=459778</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAndroidAlpha<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ColorStateList</code> uses app:alpha without <code>android:alpha</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAppTint<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>ImageView</code> or <code>ImageButton</code> uses <code>android:tint</code> instead of <code>app:tint</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForColorStateLists<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of color state lists<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatLoadingForDrawables<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableApis<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use Compat loading of compound text view drawables<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompatTextViewDrawableXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
<code>TextView</code> uses <code>android:</code> compound drawable attributes instead of <code>app:</code> ones<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseRequireInsteadOfGet<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
AndroidX added new "require____()" versions of common "get___()" APIs, such as getContext/getActivity/getArguments/etc. Rather than wrap these in something like requireNotNull(), using these APIs will allow the underlying component to try to tell you _why_ it was null, and thus yield a better error message.<br/><div class="vendor">
Vendor: Android Open Source Project (androidx.fragment.lint.fragment)<br/>
Identifier: androidx.fragment.lint.fragment<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=192731">https://issuetracker.google.com/issues/new?component=192731</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSupportActionBar<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>AppCompatActivity.setSupportActionBar</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialCode<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>SwitchMaterial</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSwitchCompatOrMaterialXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Use <code>SwitchCompat</code> from AppCompat or <code>SwitchMaterial</code> from Material library<br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingOnClickInXml<div class="issueSeparator"></div>
</div>
<div class="metadata"><div class="explanation">
Old versions of the platform do not properly support resolving <code>android:onClick</code><br/><div class="vendor">
Vendor: Android Open Source Project<br/>
Identifier: androidx.appcompat<br/>
Feedback: <a href="https://issuetracker.google.com/issues/new?component=460343">https://issuetracker.google.com/issues/new?component=460343</a><br/>
</div>
<br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="IncludedIssuesLink" onclick="reveal('IncludedIssues');">
List Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ExtraIssuesCardLink" onclick="hideid('ExtraIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/design">https://d.android.com/r/studio-ui/designer/material/design</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterNaming<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should use the name <code>content</code> for the parameter.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ComposableLambdaParameterPosition<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Composable functions with only one composable lambda parameter should place the parameter at the end of the parameter list, so it can be used as a trailing lambda.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/designer/material/iconography">https://d.android.com/r/studio-ui/designer/material/iconography</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ImplicitSamInstance<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Kotlin's support for SAM (single accessor method) interfaces lets you pass a lambda to the interface. This will create a new instance on the fly even though there is no explicit constructor call. If you pass one of these lambdas or method references into a method which (for example) stores or compares the object identity, unexpected results may happen.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>isEquivalentTo(PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). Use the right single quotation mark for apostrophes. Never use generic quotes ", ' or free-standing accents `, ´ for quotation marks, apostrophes, or primes. This can make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>