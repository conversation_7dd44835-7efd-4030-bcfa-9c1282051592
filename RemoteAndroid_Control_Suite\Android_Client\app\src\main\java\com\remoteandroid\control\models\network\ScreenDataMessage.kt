package com.remoteandroid.control.models.network

import com.google.gson.annotations.SerializedName
import com.remoteandroid.control.constants.NetworkConstants

/**
 * RemoteAndroid Control Suite - 屏幕数据消息
 * 用于传输Android端屏幕截图数据到PC端
 */
class ScreenDataMessage(
    private val screenData: ScreenData
) : MessageBase() {

    override val messageType = NetworkConstants.MESSAGE_TYPE_SCREEN_DATA
    override val data = screenData
    
    /**
     * 屏幕数据结构
     */
    data class ScreenData(
        val imageData: String,      // Base64编码的图像数据
        val width: Int,             // 图像宽度
        val height: Int,            // 图像高度
        val format: String,         // 图像格式 (JPEG/PNG/WEBP)
        val quality: Int,           // 压缩质量 (1-100)
        val frameNumber: Long = 0,  // 帧序号
        val captureTime: Long = System.currentTimeMillis(), // 捕获时间
        val deviceInfo: DeviceInfo? = null // 设备信息
    )
    
    /**
     * 设备信息结构
     */
    data class DeviceInfo(
        val deviceModel: String,    // 设备型号
        val androidVersion: String, // Android版本
        val screenDensity: Int,     // 屏幕密度
        val orientation: Int        // 屏幕方向 (0=竖屏, 1=横屏)
    )
    
    /**
     * 获取屏幕数据
     */
    fun getScreenData(): ScreenData = screenData
    
    /**
     * 获取图像数据大小
     */
    fun getImageDataSize(): Int {
        return screenData.imageData.length
    }
    
    /**
     * 检查消息是否有效
     */
    override fun isValid(): Boolean {
        return super.isValid() &&
                screenData.imageData.isNotEmpty() &&
                screenData.width > 0 &&
                screenData.height > 0 &&
                screenData.format.isNotEmpty() &&
                screenData.quality in 1..100
    }
    
    /**
     * 获取消息摘要
     */
    override fun toString(): String {
        return "ScreenDataMessage(${screenData.width}x${screenData.height}, " +
                "format=${screenData.format}, quality=${screenData.quality}%, " +
                "size=${getImageDataSize()} chars, frame=${screenData.frameNumber})"
    }
    
    companion object {
        /**
         * 创建屏幕数据消息
         */
        fun create(
            imageData: String,
            width: Int,
            height: Int,
            format: String = "JPEG",
            quality: Int = NetworkConstants.SCREEN_DATA_COMPRESSION_QUALITY,
            frameNumber: Long = 0,
            deviceInfo: DeviceInfo? = null
        ): ScreenDataMessage {
            val screenData = ScreenData(
                imageData = imageData,
                width = width,
                height = height,
                format = format,
                quality = quality,
                frameNumber = frameNumber,
                deviceInfo = deviceInfo
            )
            return ScreenDataMessage(screenData)
        }
        
        /**
         * 创建设备信息
         */
        fun createDeviceInfo(): DeviceInfo {
            return DeviceInfo(
                deviceModel = android.os.Build.MODEL,
                androidVersion = android.os.Build.VERSION.RELEASE,
                screenDensity = android.content.res.Resources.getSystem().displayMetrics.densityDpi,
                orientation = android.content.res.Resources.getSystem().configuration.orientation
            )
        }
    }
}
