using Newtonsoft.Json;
using System;

namespace RemoteAndroidControl.WPF.Models.Network
{
    /// <summary>
    /// RemoteAndroid Control Suite - 网络消息基类
    /// 定义所有网络消息的通用结构和属性
    /// </summary>
    public abstract class MessageBase
    {
        /// <summary>
        /// 消息类型
        /// </summary>
        [JsonProperty("messageType")]
        public string MessageType { get; set; } = string.Empty;

        /// <summary>
        /// 消息ID，用于请求响应匹配
        /// </summary>
        [JsonProperty("id")]
        public string MessageId { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// 时间戳
        /// </summary>
        [JsonProperty("timestamp")]
        public long Timestamp { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

        /// <summary>
        /// 消息数据
        /// </summary>
        [JsonProperty("data")]
        public virtual object? Data { get; set; }

        /// <summary>
        /// 序列化为JSON字符串
        /// </summary>
        /// <returns>JSON字符串</returns>
        public string ToJson()
        {
            return JsonConvert.SerializeObject(this);
        }

        /// <summary>
        /// 从JSON字符串反序列化
        /// </summary>
        /// <typeparam name="T">消息类型</typeparam>
        /// <param name="json">JSON字符串</param>
        /// <returns>消息对象</returns>
        public static T? FromJson<T>(string json) where T : MessageBase
        {
            try
            {
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// 消息类型常量
    /// </summary>
    public static class MessageTypes
    {
        /// <summary>
        /// 心跳消息
        /// </summary>
        public const string HEARTBEAT = "HEARTBEAT";

        /// <summary>
        /// 连接请求
        /// </summary>
        public const string CONNECT = "connect";

        /// <summary>
        /// 连接响应
        /// </summary>
        public const string CONNECT_RESPONSE = "connect_response";

        /// <summary>
        /// 屏幕数据
        /// </summary>
        public const string SCREEN_DATA = "SCREEN_DATA";

        /// <summary>
        /// 点击指令
        /// </summary>
        public const string CLICK = "CLICK";

        /// <summary>
        /// 密码消息
        /// </summary>
        public const string PASSWORD = "PASSWORD";

        /// <summary>
        /// 文件消息
        /// </summary>
        public const string FILE = "FILE";

        /// <summary>
        /// 错误消息
        /// </summary>
        public const string ERROR = "ERROR";

        /// <summary>
        /// 请求屏幕录制权限
        /// </summary>
        public const string REQUEST_SCREEN_CAPTURE = "REQUEST_SCREEN_CAPTURE";

        /// <summary>
        /// 屏幕录制权限响应
        /// </summary>
        public const string SCREEN_CAPTURE_RESPONSE = "SCREEN_CAPTURE_RESPONSE";
    }
}
